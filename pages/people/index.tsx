import { useCallback, useEffect, useState } from 'react';

import PlusIcon from '@/assets/outline/plus';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import { Dialog } from '@/components/common/dialog';
import DeleteModal from '@/components/common/modals/deleteModal';
import Layout from '@/components/common/sidebar/layout';
import CommonTable, { ManageCellRenderer } from '@/components/common/table';
import CreateEmployeeModal from '@/components/people/modals/createEmployeeModal';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { TEmployeeData } from '@/interfaces/people';
import { getValueOrDefault } from '@/utils/general';
import { hasAccess } from '@/utils/roleAccessConfig';

const PeopleHub = () => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const user = useAuthStore((state) => state.user);

  const { data, isLoading, error, reFetch } = useFetch(
    accessToken,
    'employees',
  );

  const { data: employeeTitle } = useFetch<{
    records: {
      id: string;
      name: string;
    }[];
  }>(accessToken, 'employee/titles');

  const [activeTab, setActiveTab] = useState<number>(0);
  const [createEmployee, setCreateEmployee] = useState(false);
  const [editEmployee, setEditEmployee] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<
    undefined | TEmployeeData
  >(undefined);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const {
    deleteData,
    response: onDelete,
    isLoading: deleteLoading,
  } = useDelete();

  useEffect(() => {
    if (onDelete) {
      setShowDeleteModal(false);
      reFetch();
    }
  }, [onDelete]);

  const handleEdit = (rowData: TEmployeeData) => {
    setSelectedEmployee(rowData);
    setEditEmployee(true);
  };

  const handleDeleteModal = (rowData: TEmployeeData) => {
    setSelectedEmployee(rowData);
    setShowDeleteModal(true);
  };

  const handleDelete = () => {
    async function fetch() {
      await deleteData(accessToken, `employees/${selectedEmployee?.id}`);
    }
    fetch();
  };

  const getPeopleColumns = useCallback(() => {
    const peopleColumns: any = [
      {
        headerName: 'ID',
        field: 'employee_id',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: any) => {
          return params.value;
        },
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'employee_id'),
        filter: 'agMultiColumnFilter',
      },
      {
        headerName: 'Name',
        field: 'name',
        resizable: true,
        sortable: true,
        valueFormatter: (params: any) => getValueOrDefault(params.data, 'name'),
        filter: 'agMultiColumnFilter',
      },
      {
        headerName: 'Job title',
        field: 'job_title',
        resizable: true,
        sortable: false,
        valueFormatter: (params: any) => params.data.job_title?.name || '',
        filter: 'agMultiColumnFilter',
      },
      {
        headerName: 'E-mail',
        field: 'email',
        resizable: true,
        sortable: true,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'email'),
        filter: 'agMultiColumnFilter',
      },
      {
        headerName: 'Departments',
        field: 'departments',
        filter: 'agMultiColumnFilter',
        filterValueGetter: (params: any) => {
          return params?.data?.departments
            ? params?.data?.departments
                ?.map((department: any) => department.name)
                .join(', ')
            : '-';
        },
        getQuickFilterText: (params: any) => {
          return params?.data?.departments
            ? params?.data?.departments
                ?.map((department: any) => department.name)
                .join(', ')
            : '-';
        },
        valueFormatter: (params: any) => {
          return params?.data?.departments
            ? params?.data?.departments
                ?.map((department: any) => department.name)
                .join(', ')
            : '-';
        },
      },
      {
        headerName: 'Processes',
        field: 'processes',
        filter: 'agMultiColumnFilter',
        filterValueGetter: (params: any) => {
          return params?.data?.processes
            ? params?.data?.processes
                ?.map((process: any) => process.name)
                .join(', ')
            : '-';
        },
        getQuickFilterText: (params: any) => {
          return params?.data?.processes
            ? params?.data?.processes
                ?.map((process: any) => process.name)
                .join(', ')
            : '-';
        },
        valueFormatter: (params: any) => {
          return params?.data?.processes
            ? params?.data?.processes
                ?.map((process: any) => process.name)
                .join(', ')
            : '-';
        },
      },
      {
        headerName: 'Date of joining',
        field: 'date_of_joining',
        resizable: true,
        sortable: true,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'date_of_joining'),
        filter: 'agMultiColumnFilter',
      },
      {
        headerName: 'Date of leaving',
        field: 'date_of_leaving',
        resizable: true,
        sortable: true,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'date_of_leaving'),
        filter: 'agMultiColumnFilter',
      },
      {
        headerName: 'Status',
        field: 'status',
        resizable: true,
        sortable: true,
        filter: 'agMultiColumnFilter',
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'status'),
      },
    ];

    hasAccess(AccessActions.CanAddOrEditPeople, user) &&
      peopleColumns.push({
        headerName: 'Manage',
        field: 'manage',
        cellRenderer: (params: any) => (
          <ManageCellRenderer
            rowData={params.data}
            handleEdit={handleEdit}
            handleDelete={handleDeleteModal}
            hideDelete={!hasAccess(AccessActions.IsPeopleAdmin, user)}
          />
        ),
        width: 100,
        pinned: 'right',
        filter: false,
      });
    return peopleColumns;
  }, [user]);

  const breadcrumbData = [
    {
      name: 'People hub',
      link: '/people',
    },
  ];

  return (
    <Layout>
      {editEmployee && selectedEmployee && (
        <Dialog open={editEmployee} onOpenChange={setEditEmployee}>
          <CreateEmployeeModal
            edit
            employeeData={selectedEmployee}
            setOpenEdit={setEditEmployee}
            reFetch={reFetch}
            employeeTitle={employeeTitle?.records}
          />
        </Dialog>
      )}
      {createEmployee && (
        <Dialog open={createEmployee} onOpenChange={setCreateEmployee}>
          <CreateEmployeeModal
            setOpenEdit={setCreateEmployee}
            reFetch={reFetch}
            employeeTitle={employeeTitle?.records}
          />
        </Dialog>
      )}
      {showDeleteModal && (
        <Dialog
          open={showDeleteModal}
          onOpenChange={() => setShowDeleteModal(false)}
        >
          <DeleteModal
            title={`Delete Employee`}
            infoText={`Are you sure you want to delete this employee?`}
            btnText={'Delete'}
            onClick={handleDelete}
            btnLoading={deleteLoading}
          />
        </Dialog>
      )}
      <div className="flex items-start justify-between my-5">
        <div className="flex flex-col">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            People Hub
          </div>
        </div>
      </div>
      <div className="mb-5 relative">
        {hasAccess(AccessActions.CanAddOrEditPeople, user) && (
          <div className="flex gap-4 absolute top-0 right-0 z-10">
            <PrimaryButton
              icon={<PlusIcon color="white" />}
              text="Add Employee"
              buttonClasses="!px-5 !py-2"
              onClick={() => setCreateEmployee(true)}
            />
          </div>
        )}

        <CommonTable
          data={data}
          columnDefs={getPeopleColumns()}
          handleRowClick={(e) => {
            setSelectedEmployee(e?.data);
          }}
          searchPlaceholder={
            'Search by ID, name, department, process or status'
          }
          isLoading={isLoading}
        />
      </div>
    </Layout>
  );
};

export default PeopleHub;
