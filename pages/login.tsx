import BPRLogo from '@/assets/logo';
import PrimaryButton from '@/components/common/button/primaryButton';
import { useAuthStore } from '@/globalProvider/authStore';
import { GET_ORGANIZATION } from '@/utils/api';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import login from '../assets/login.svg';
import BPRGearLogo from '@/assets/gearLogo';

const SignIn: React.FC = () => {
  const [showNotification, setShowNotification] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessgae, setSuccessMessage] = useState<string | null>(null);
  // const [isLoading, setIsLoading] = useState<boolean | null>(false);
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [loginStep, setLoginStep] = useState(1);
  const [orgOptions, setOrgOptions] = useState([]);
  const setOrg = useAuthStore((state) => state.setOrg);
  const orgInfo = useAuthStore((state) => state.orgInfo);
  const user = useAuthStore((state) => state.user);
  const accessToken = useAuthStore((state) => state.accessToken);
  const { isLoading, setIsLoading } = useAuthStore((state) => state);

  // useEffect(() => {
  // 	if (error) {
  // 		console.log(error.message);
  // 		setErrorMessage("Access denied. Please Contact Admin");
  // 		setShowNotification(true);
  // 		return;
  // 	}
  // }, [error]);

  const router = useRouter();

  useEffect(() => {
    if (accessToken && user?.id) {
      router.push('/standard');
    } else if (!isLoading) {
      router.push('/login');
    }
  }, [accessToken, user, isLoading]);

  const handleLogin = async (
    org_info: { external_id: any; is_guest: any },
    email: string,
  ) => {
    let params = {
      organization: org_info.external_id,
      ...(!!org_info.is_guest
        ? { connection: 'email', login_hint: email }
        : {}),
    };

    setOrg(org_info.external_id ? org_info.external_id : email);

    try {
      // Build query parameters
      const queryParams = new URLSearchParams(params).toString();

      if (params.organization) {
        if (accessToken) {
          router.push('document');
        } else {
          window.location.href = `/api/auth/login?${queryParams}`;
        }
      }

      // Redirect the user with custom parameters
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  const handleOrganizationList = async (option: any) => {
    setIsLoading(true);
    userEmail && (await handleLogin(option, userEmail));
  };

  const handleSubmit = async (event: any) => {
    event.preventDefault();
    setIsLoading(true);

    setErrorMessage(null);
    const formData = new FormData(event.target);
    const email = formData.get('email') as string | null;
    if (email) {
      setUserEmail(email);
      let headers = {
        'Content-Type': 'application/json',
        'api-key': btoa(email),
      };

      const res = await fetch(GET_ORGANIZATION, { headers: headers });

      if (res.status == 200) {
        let res_json = await res.json();
        if (res_json.records.length == 1) {
          setIsLoading(true);
          let org_info = res_json.records[0];
          await handleLogin(org_info, email);
        } else {
          setIsLoading(false);
          setLoginStep(2);
          setOrgOptions(res_json.records);
        }
      } else {
        let res_json = null;
        try {
          res_json = await res.json();
          res_json = res_json['error'];
        } catch {
          res_json = 'Something went wrong';
        }
        setErrorMessage(`Error: ${res_json}`);
        setShowNotification(true);
        setIsLoading(false);
      }
    } else {
      setIsLoading(false);
      setErrorMessage('Invalid email');
      setShowNotification(true);
    }
  };

  if (isLoading) return null;

  return (
    <>
      <div>
        <div className="flex flex-wrap items-center">
          <div className="w-full xl:w-1/2">
            {loginStep == 1 && (
              <>
                <div className="mx-5 mt-10 mb-4 p-4 sm:mx-20 sm:p-8 xl:p-6">
                  <div className="flex flex-col justify-center">
                    <div
                      className="mb-2 bg-primary-100 rounded-lg flex items-center justify-center"
                      style={{ width: '60px', height: '60px' }}
                    >
                      <BPRGearLogo width="36" height="36" />
                    </div>

                    <h2 className="mb-8 mt-4 text-[2rem] font-[700] text-black text-opacity-90 dark:text-white sm:text-title-sm">
                      Welcome back!
                    </h2>
                  </div>

                  {errorMessage && (
                    <p className="text-red-400 text-sm mb-4">{errorMessage}</p>
                  )}

                  <form onSubmit={handleSubmit}>
                    <div className="mb-6">
                      <label className="mb-2.5 block font-medium text-black text-opacity-90 dark:text-white">
                        Email
                      </label>
                      <div className="relative">
                        <input
                          name="email"
                          type="email"
                          required
                          placeholder="Enter your work email here"
                          className="w-full rounded-lg border border-black border-opacity-30 bg-transparent py-3 pl-4 pr-10 text-black outline-none focus:border-primary focus-visible:shadow-none dark:border-form-strokedark dark:bg-form-input dark:text-white dark:focus:border-primary"
                        />

                        <span className="absolute right-4 top-3.5">
                          <svg
                            className="fill-current"
                            width="22"
                            height="22"
                            viewBox="0 0 22 22"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g opacity="0.5">
                              <path
                                d="M19.2516 3.30005H2.75156C1.58281 3.30005 0.585938 4.26255 0.585938 5.46567V16.6032C0.585938 17.7719 1.54844 18.7688 2.75156 18.7688H19.2516C20.4203 18.7688 21.4172 17.8063 21.4172 16.6032V5.4313C21.4172 4.26255 20.4203 3.30005 19.2516 3.30005ZM19.2516 4.84692C19.2859 4.84692 19.3203 4.84692 19.3547 4.84692L11.0016 10.2094L2.64844 4.84692C2.68281 4.84692 2.71719 4.84692 2.75156 4.84692H19.2516ZM19.2516 17.1532H2.75156C2.40781 17.1532 2.13281 16.8782 2.13281 16.5344V6.35942L10.1766 11.5157C10.4172 11.6875 10.6922 11.7563 10.9672 11.7563C11.2422 11.7563 11.5172 11.6875 11.7578 11.5157L19.8016 6.35942V16.5688C19.8703 16.9125 19.5953 17.1532 19.2516 17.1532Z"
                                fill=""
                              />
                            </g>
                          </svg>
                        </span>
                      </div>
                    </div>
                    <PrimaryButton
                      type="submit"
                      text="Continue"
                      buttonClasses="!px-5 !py-2"
                      isLoading={isLoading}
                      width="100%"
                    />
                  </form>
                  <div className="mt-8">
                    <p className="text-md">
                      By signing in, you agree to our{' '}
                      <Link
                        href="https://www.bprhub.com/terms-of-use"
                        className="font-medium text-primary-400"
                      >
                        Terms of Service
                      </Link>{' '}
                      and{' '}
                      <Link
                        href="https://www.bprhub.com/privacy"
                        className="font-medium text-primary-400"
                      >
                        Privacy Policy
                      </Link>
                    </p>
                  </div>
                </div>
              </>
            )}
            {loginStep == 2 && orgOptions && (
              <div className="mx-5 my-10 p-8 sm:mx-20 sm:p-12.5 xl:p-12 shadow-default rounded-md">
                {/* <img width={150} src={Logo} alt="Logo" /> */}

                <h2 className="mb-6 mt-8 text-2xl font-bold text-black dark:text-white sm:text-title-sm">
                  Select Organizations
                </h2>

                {orgOptions.map((item: any) => {
                  return (
                    <div
                      role="button"
                      key={item.external_id}
                      onClick={() => handleOrganizationList(item)}
                      className={`flex items-center w-1/2 mb-3 p-3 py-1 pl-4 pr-1 border rounded-lg ${
                        isLoading
                          ? 'opacity-50 pointer-events-none'
                          : 'hover:bg-white-150'
                      }`}
                    >
                      {item.name}
                      {/* Fix comment */}
                      <div className="ml-auto place-items-center justify-self-end">
                        <button
                          className="relative h-10 max-h-[40px] w-10 max-w-[40px] text-center"
                          type="button"
                        >
                          <span className="absolute transform -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2">
                            <svg
                              className="w-6 h-6 text-gray-800 dark:text-white"
                              aria-hidden="true"
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              fill="none"
                              viewBox="0 0 24 24"
                            >
                              <path
                                stroke="currentColor"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M16 12H4m12 0-4 4m4-4-4-4m3-4h2a3 3 0 0 1 3 3v10a3 3 0 0 1-3 3h-2"
                              />
                            </svg>
                          </span>
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
          <div className="w-full hidden xl:block xl:w-1/2 h-screen">
            <Image
              src={login}
              alt={''}
              className="h-screen object-cover w-full"
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default SignIn;
