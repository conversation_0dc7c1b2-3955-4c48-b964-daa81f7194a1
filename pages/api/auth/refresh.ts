// import { getSession, updateSession } from '@auth0/nextjs-auth0';
import { NextApiRequest, NextApiResponse } from 'next';
import auth0 from './auth0';
import axios from 'axios';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  try {
    const session = await auth0.getSession(req, res);

    if (!session) {
      return res.status(401).json({ error: 'Not authenticated' });
    }

    const refreshToken = session.refreshToken;
    const organization = session.user?.org_id;
    console.log(refreshToken,organization)
    // Ensure org_id is stored in the session

    // Call Auth0 to refresh the token with `organization`
    const response = await axios.post(
          `${process.env.AUTH0_ISSUER_BASE_URL}/oauth/token`,
          {
            grant_type: "refresh_token",
            client_id: process.env.AUTH0_CLIENT_ID,
            client_secret: process.env.AUTH0_CLIENT_SECRET,
            refresh_token: refreshToken,
            organization, // 👈 Ensure the organization is sent
          },
          { headers: { "Content-Type": "application/json" } }
        );

    const newAccessToken = response.data.access_token;
    const newRefreshToken = response.data.refresh_token || refreshToken; // Use new token if issued
    

    await auth0.updateSession(req, res, {
      ...session,
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
    });

    res.json({
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
    });
  } catch (error) {
    console.error('Error refreshing token:', error);
    res.status(500).json({ error: 'Failed to refresh access token' });
  }
}
