import { useCallback, useEffect, useState } from 'react';

import PlusIcon from '@/assets/outline/plus';
import CreateAssetModal from '@/components/asset/modals/createAssetModal';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import { Dialog } from '@/components/common/dialog';
import DeleteModal from '@/components/common/modals/deleteModal';
import Layout from '@/components/common/sidebar/layout';
import CommonTable, { ManageCellRenderer } from '@/components/common/table';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { TAssetData } from '@/interfaces/asset';
import { getValueOrDefault } from '@/utils/general';
import { hasAccess } from '@/utils/roleAccessConfig';
import { formatDate, getPeriodFromMonth } from '@/utils/time';

const AssetHub = () => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const user = useAuthStore((state) => state.user);

  const { data, isLoading, error, reFetch } = useFetch(accessToken, 'assets');

  const [activeTab, setActiveTab] = useState<number>(0);
  const [createAsset, setCreateAsset] = useState(false);
  const [editAsset, setEditAsset] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<undefined | TAssetData>(
    undefined,
  );
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { deleteData, response: onDelete } = useDelete();

  useEffect(() => {
    if (onDelete) {
      setShowDeleteModal(false);
      reFetch();
    }
  }, [onDelete]);

  const handleEdit = (rowData: TAssetData) => {
    setSelectedAsset(rowData);
    setEditAsset(true);
  };

  const handleDeleteModal = (rowData: TAssetData) => {
    setSelectedAsset(rowData);
    setShowDeleteModal(true);
  };

  const handleDelete = () => {
    async function fetch() {
      accessToken &&
        (await deleteData(accessToken, `assets/${selectedAsset?.id}`));
    }
    fetch();
  };

  const getAssetColumns = useCallback(() => {
    const assetColumns: any = [
      {
        headerName: 'ID',
        field: 'asset_id',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: any) => {
          return params.value;
        },
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'asset_id'),
        filter: false,
      },
      {
        headerName: 'Asset name',
        field: 'name',
        resizable: true,
        valueFormatter: (params: any) => getValueOrDefault(params.data, 'name'),
        filter: false,
      },
      {
        headerName: 'Description',
        field: 'description',
        resizable: true,
        filter: 'agMultiColumnFilter',
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'description'),
      },
      {
        headerName: 'Location',
        field: 'location',
        filter: 'agMultiColumnFilter',
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'location'),
      },
      {
        headerName: 'Asset owner',
        field: 'owner',
        filter: 'agMultiColumnFilter',
        sortable: false,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data.owner, 'name'),
      },
      {
        headerName: 'Purchase Date',
        field: 'purchase_date',
        resizable: true,
        valueFormatter: (params: any) =>
          formatDate(getValueOrDefault(params.data, 'purchase_date'), false),
        filter: false,
      },
      {
        headerName: 'Calibration Date',
        field: 'calibration_date',
        resizable: true,
        valueFormatter: (params: any) =>
          formatDate(getValueOrDefault(params.data, 'calibration_date'), false),
        filter: false,
      },
      {
        headerName: 'Calibration Cert no.',
        field: 'calibration_cert_num',
        resizable: true,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'calibration_cert_num'),
        filter: false,
      },
      {
        headerName: 'Calibration period',
        field: 'calibration_period',
        resizable: true,
        valueFormatter: (params: any) =>
          getPeriodFromMonth(
            getValueOrDefault(params.data, 'calibration_period'),
          ),
        filter: false,
      },
      {
        headerName: 'Status',
        field: 'status',
        resizable: true,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'status'),
        filter: false,
      },
    ];
    hasAccess(AccessActions.CanAddOrEditAssets, user) &&
      assetColumns.push({
        headerName: 'Manage',
        field: 'manage',
        cellRenderer: (params: any) => (
          <ManageCellRenderer
            rowData={params.data}
            handleEdit={handleEdit}
            handleDelete={handleDeleteModal}
            hideDelete={!hasAccess(AccessActions.IsAssetAdmin, user)}
          />
        ),
        width: 100,
        pinned: 'right',
        filter: false,
      });
    return assetColumns;
  }, [user]);

  const breadcrumbData = [
    {
      name: 'Asset hub',
      link: '/asset',
    },
  ];

  return (
    <Layout>
      {createAsset && (
        <Dialog open={createAsset} onOpenChange={setCreateAsset}>
          <CreateAssetModal setOpenEdit={setCreateAsset} reFetch={reFetch} />
        </Dialog>
      )}
      {editAsset && selectedAsset && (
        <Dialog
          open={editAsset}
          onOpenChange={() => {
            setEditAsset(false);
          }}
        >
          <CreateAssetModal
            edit={true}
            assetData={selectedAsset}
            setOpenEdit={setEditAsset}
            reFetch={reFetch}
          />
        </Dialog>
      )}
      {showDeleteModal && (
        <Dialog
          open={showDeleteModal}
          onOpenChange={() => setShowDeleteModal(false)}
        >
          <DeleteModal
            title={`Delete Asset`}
            infoText={`Are you sure you want to delete this asset?`}
            btnText={'Delete'}
            onClick={handleDelete}
          />
        </Dialog>
      )}
      <div className="flex items-start justify-between my-5">
        <div className="flex flex-col">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            Asset Hub
          </div>
        </div>
      </div>
      <div className="mb-5">
        <CommonTable
          data={data}
          columnDefs={getAssetColumns()}
          searchPlaceholder="Search by ID, name, description, location, owner or status"
          isLoading={isLoading}
          searchRightSideElement={
            hasAccess(AccessActions.CanAddOrEditAssets, user) ? (
              <div className="flex gap-4">
                <PrimaryButton
                  text="Add Asset"
                  buttonClasses="!px-5 !py-2"
                  onClick={() => {
                    setCreateAsset(true);
                  }}
                  icon={<PlusIcon color="white" />}
                />
              </div>
            ) : (
              <></>
            )
          }
        />
      </div>
    </Layout>
  );
};

export default AssetHub;
