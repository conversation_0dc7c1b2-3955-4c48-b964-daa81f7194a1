import '@/styles/globals.css';
import './index.css';
import 'react-toastify/dist/ReactToastify.css';
import 'react-datepicker/dist/react-datepicker.css';

import { Inter } from 'next/font/google';

import ToastWrapper from '@/components/common/toast';
import { TooltipProvider } from '@/components/common/tooltip';
import { UserProvider } from '@auth0/nextjs-auth0/client';

import GlobalProvider from '../globalProvider/globalProvider';
import GlobalLoader from './globalLoader';

import type { AppProps } from 'next/app';
const inter = Inter({ subsets: ['latin'] });

export default function App({ Component, pageProps }: AppProps) {
  return (
    <main className={inter.className}>
      <UserProvider>
        <GlobalProvider>
          <GlobalLoader />
          <TooltipProvider>
            <Component {...pageProps} />
            <ToastWrapper />
          </TooltipProvider>
        </GlobalProvider>
      </UserProvider>
    </main>
  );
}
