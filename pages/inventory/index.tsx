import { useCallback, useEffect, useState } from 'react';

import PlusIcon from '@/assets/outline/plus';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import { Dialog } from '@/components/common/dialog';
import DeleteModal from '@/components/common/modals/deleteModal';
import Layout from '@/components/common/sidebar/layout';
import CommonTable, { ManageCellRenderer } from '@/components/common/table';
import CreateMaterialModal from '@/components/inventory/modals/createInventoryModal';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { TMaterialData } from '@/interfaces/material';
import { getValueOrDefault } from '@/utils/general';
import { hasAccess } from '@/utils/roleAccessConfig';

export interface ISelectboxData {
  id?: string;
  name: string;
}
const InventoryHub = () => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const user = useAuthStore((state) => state.user);

  const { data, isLoading, error, reFetch } = useFetch(
    accessToken,
    'materials',
  );

  const { data: materialType } = useFetch<{ records: ISelectboxData[] }>(
    accessToken,
    'materials/types',
  );
  const { data: materialCategories } = useFetch<{ records: ISelectboxData[] }>(
    accessToken,
    'materials/categories',
  );
  const { data: materialUnits } = useFetch<{ records: ISelectboxData[] }>(
    accessToken,
    'materials/units',
  );

  const [createMaterial, setCreateMaterial] = useState(false);
  const [editMaterial, setEditMaterial] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState<
    undefined | TMaterialData
  >(undefined);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { deleteData, response: onDelete } = useDelete();

  useEffect(() => {
    if (onDelete) {
      setShowDeleteModal(false);
      reFetch();
    }
  }, [onDelete]);

  const handleEdit = (rowData: TMaterialData) => {
    setSelectedMaterial(rowData);
    setEditMaterial(true);
  };

  const handleDeleteModal = (rowData: TMaterialData) => {
    setSelectedMaterial(rowData);
    setShowDeleteModal(true);
  };

  const handleDelete = () => {
    async function fetch() {
      await deleteData(accessToken, `materials/${selectedMaterial?.id}`);
    }
    fetch();
  };

  const getInventoryColumns = useCallback(() => {
    const inventoryColumns: any = [
      {
        headerName: 'ID',
        field: 'material_id',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: any) => {
          return params.value;
        },
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'material_id'),
        filter: 'agMultiColumnFilter',
      },
      {
        headerName: 'Material name',
        field: 'name',
        resizable: true,
        valueFormatter: (params: any) => getValueOrDefault(params.data, 'name'),
        filter: 'agMultiColumnFilter',
      },
      {
        headerName: 'Type',
        field: 'type',
        resizable: true,
        valueFormatter: (params: any) => getValueOrDefault(params.data, 'type'),
        filter: 'agMultiColumnFilter',
      },
      {
        headerName: 'Category',
        field: 'category',
        resizable: true,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'category'),
        filter: 'agMultiColumnFilter',
      },
      {
        headerName: 'UOM',
        field: 'units',
        resizable: true,
        filter: 'agMultiColumnFilter',
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'units'),
      },
    ];
    hasAccess(AccessActions.CanAddOrEditInventory, user) &&
      inventoryColumns.push({
        headerName: 'Manage',
        field: 'manage',
        cellRenderer: (params: any) => (
          <ManageCellRenderer
            rowData={params.data}
            handleEdit={handleEdit}
            handleDelete={handleDeleteModal}
            hideDelete={!hasAccess(AccessActions.IsInventoryAdmin, user)}
          />
        ),
        width: 100,
        pinned: 'right',
        filter: false,
      });
    return inventoryColumns;
  }, [user]);

  const breadcrumbData = [
    {
      name: 'Inventory hub',
      link: '/inventory',
    },
  ];

  return (
    <Layout>
      {editMaterial && selectedMaterial && (
        <Dialog
          open={editMaterial}
          onOpenChange={() => {
            setEditMaterial(false);
          }}
        >
          <CreateMaterialModal
            edit
            materialData={selectedMaterial}
            setOpenEdit={setEditMaterial}
            reFetch={reFetch}
            materialType={materialType?.records}
            materialCategories={materialCategories?.records}
            materialUnits={materialUnits?.records}
          />
        </Dialog>
      )}
      {createMaterial && (
        <Dialog
          open={createMaterial}
          onOpenChange={() => {
            setCreateMaterial(false);
          }}
        >
          <CreateMaterialModal
            setOpenEdit={setCreateMaterial}
            reFetch={reFetch}
            materialCategories={materialCategories?.records}
            materialType={materialType?.records}
            materialUnits={materialUnits?.records}
          />
        </Dialog>
      )}
      {showDeleteModal && (
        <Dialog
          open={showDeleteModal}
          onOpenChange={() => setShowDeleteModal(false)}
        >
          <DeleteModal
            title={`Delete Material`}
            infoText={`Are you sure you want to delete this material?`}
            btnText={'Delete'}
            onClick={handleDelete}
          />
        </Dialog>
      )}
      <div className="flex items-start justify-between my-5">
        <div className="flex flex-col">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            Inventory Hub
          </div>
        </div>
      </div>
      <div className="mb-5">
        <CommonTable
          data={data}
          columnDefs={getInventoryColumns()}
          isLoading={isLoading}
          searchRightSideElement={
            hasAccess(AccessActions.CanAddOrEditInventory, user) ? (
              <div className="flex gap-4">
                <PrimaryButton
                  text="Add Material"
                  buttonClasses="!px-5 !py-2"
                  onClick={() => setCreateMaterial(true)}
                  icon={<PlusIcon color="white" />}
                />
              </div>
            ) : (
              <></>
            )
          }
        />
      </div>
    </Layout>
  );
};

export default InventoryHub;
