import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import { useEffect, useState } from 'react';

import { IAxiosHeader } from '@/utils/type';

type UseDelete = <T = unknown, B = unknown>() => {
  deleteData: (
    accessToken: string | null,
    url: string,
    body?: B,
  ) => Promise<void>;
  response: T | null;
  error: Error | null;
  isLoading: boolean;
};
export const useDelete: UseDelete = <T, B>() => {
  const [error, setError] = useState<AxiosError | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [response, setResponse] = useState<T | null>(null);

  const deleteData = async (
    accessToken: string | null,
    path: string,
    body?: B,
  ) => {
    setIsLoading(true);

    const mockUser =
      typeof window !== 'undefined'
        ? localStorage.getItem('x-mock-user')
        : null;

    const headers: IAxiosHeader = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${accessToken}`,
      ...(!!mockUser ? { 'x-mock-user': mockUser } : {}),
    };
    const baseUrl = process.env.NEXT_PUBLIC_URL;
    const productVersion = process.env.NEXT_PUBLIC_VERSION;
    const options: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${baseUrl}/${productVersion}/${path}`,
      headers: headers,
      data: body,
    };
    if (accessToken) {
      try {
        const response: AxiosResponse<T> = await axios.request<T>(options);
        setResponse(response.data);
        console.log('--- DELETE Hook Response ---', response.data);
      } catch (err) {
        const axiosError = err as AxiosError;
        setError(axiosError);
        console.error('Oops! Something went wrong', axiosError.message);
      } finally {
        setIsLoading(false);
      }
    }
  };

  return { deleteData, response, isLoading, error };
};
