import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from "axios";
import { useState } from "react";
import { toast } from "react-toastify";

type UsePut = <T = unknown, B = Record<string, unknown>>() => {
  putData: (accessToken: string, path: string, body: B) => Promise<void>;
  response: T | null;
  error: AxiosError | null;
  isLoading: boolean;
};

interface ErrorResponse {
  detail?: string;
  error?: string;
}

export const usePut: UsePut = <T, B>() => {
  const [error, setError] = useState<AxiosError | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [response, setResponse] = useState<T | null>(null);

  const putData = async (accessToken: string, path: string, body: B) => {
    setIsLoading(true);

    const mockUser =
      typeof window !== "undefined"
        ? localStorage.getItem("x-mock-user")
        : null;

    const headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
      ...(!!mockUser ? { "x-mock-user": mockUser } : {}),
    };

    const options: AxiosRequestConfig = {
      method: "PUT",
      url: `${process.env.NEXT_PUBLIC_URL}/${process.env.NEXT_PUBLIC_VERSION}/${path}`,
      headers: headers,
      data: body,
    };

    try {
      const res: AxiosResponse<T> = await axios.request<T>(options);
      setResponse(res.data);
      console.log("--- PUT Hook Response ---", res.data);
    } catch (err) {
      handleAxiosError(err as AxiosError);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAxiosError = (error: AxiosError) => {
    const errorData = error.response?.data as ErrorResponse;

    if (error.response) {
      switch (error.response.status) {
        case 401:
          window.location.reload();
          break;
        case 400:
        case 500:
          toast.error(
            errorData?.detail ||
              errorData?.error ||
              "An unknown error occurred",
          );
          break;
        case 404:
          toast.error("Not found");
          break;
        case 403:
          setError(error);
          break;
        default:
          toast.error(error.message);
          setError(error);
      }
    } else {
      toast.error("Network error or request timeout.");
    }

    setError(error);
    console.error("Error:", error.message);
  };

  return { putData, response, error, isLoading };
};
