import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from "axios";
import { useState } from "react";

type UseSearch = <T = unknown, Q = Record<string, string>>(
  endpoint: string
) => {
  search: (accessToken: string, query: string) => Promise<T>;
  isLoading: boolean;
  error: AxiosError | null;
};

// Generic Search Hook
const useSearch: UseSearch = <T, Q>(endpoint: string) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<AxiosError | null>(null);

  const baseUrl = process.env.NEXT_PUBLIC_URL;
  const productVersion = process.env.NEXT_PUBLIC_VERSION;

  const search = async (accessToken: string, query: string): Promise<T> => {
    setIsLoading(true);

    const options: AxiosRequestConfig = {
      method: "GET",
      url: `${baseUrl}/${productVersion}/${endpoint}?search=${query}`,
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    };

    try {
      const response: AxiosResponse<T> = await axios.request<T>(options);
      return response.data;
    } catch (err) {
      const axiosError = err as AxiosError;
      setError(axiosError);
      console.error("Error fetching data:", axiosError.message);
      throw axiosError;
    } finally {
      setIsLoading(false);
    }
  };

  return { search, isLoading, error };
};

export default useSearch;
