import { useState } from 'react';

const useDownload = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  // const token = localStorage.getItem("token");

  const downloadFile = (
    accessToken: string,
    endpoint: string,
    filename = 'download',
  ) => {
    setLoading(true);
    setError(null);

    fetch(
      `${process.env.NEXT_PUBLIC_URL}/${process.env.NEXT_PUBLIC_VERSION}/${endpoint}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
      },
    )
      .then((response) => {
        if (!response.ok) {
          throw new Error('Failed to fetch the file');
        }
        return response.blob(); // Convert response to Blob
      })
      .then((blob) => {
        // Create a URL for the Blob
        const fileUrl = window.URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = fileUrl;
        a.download = filename;
        document.body.appendChild(a);

        a.click();

        // Cleanup
        a.remove();
        window.URL.revokeObjectURL(fileUrl);
      })
      .catch((err) => {
        setError(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return { downloadFile, loading, error };
};

export default useDownload;
