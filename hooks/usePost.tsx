import axios, { AxiosError, AxiosRequestConfig } from "axios";
import { useState } from "react";
import { toast } from "react-toastify";

type UsePost = <T = unknown, B = Record<string, unknown>>() => {
  postData: (accessToken: string, path: string, body: B) => Promise<void>;
  response: T | null;
  error: AxiosError | null;
  isLoading: boolean;
};

interface ErrorResponse {
  detail?: string;
  error?: string;
}

// A generic POST hook
export const usePost: UsePost = <T, B>() => {
  const [error, setError] = useState<AxiosError | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [response, setResponse] = useState<T | null>(null);

  const postData = async (accessToken: string, path: string, body: B) => {
    setIsLoading(true);

    const mockUser =
      typeof window !== "undefined"
        ? localStorage.getItem("x-mock-user")
        : null;

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
      ...(!!mockUser ? { "x-mock-user": mockUser } : {}),
    };

    const baseUrl = process.env.NEXT_PUBLIC_URL;
    const productVersion = process.env.NEXT_PUBLIC_VERSION;
    const options: AxiosRequestConfig = {
      method: "POST",
      url: `${baseUrl}/${productVersion}/${path}`,
      headers: headers,
      data: body, // Use "data" instead of "body" for axios
    };

    try {
      const res = await axios.request<T>(options);
      setResponse(res.data);
      console.log("--- POST Hook Response ---", res.data);
    } catch (err) {
      const axiosError = err as AxiosError;
      const errorData = axiosError.response?.data as ErrorResponse;

      if (axiosError.response) {
        if (axiosError.response.status === 401) {
          window.location.reload();
        } else if (axiosError.response.status === 400) {
          console.log("axiosError", axiosError.response.data);
          toast.error(
            errorData?.detail ||
              errorData?.error ||
              "An unknown error occurred",
          );
        } else if (axiosError.response.status === 404) {
          toast.error("Not found");
        } else if (axiosError.response.status === 403) {
          setError(axiosError);
        } else {
          toast.error(axiosError.message);
          setError(axiosError);
        }
      } else {
        toast.error("Network error or request timeout.");
      }
      setError(err as AxiosError);
      console.error("Oops! Something went wrong:", (err as AxiosError).message);
    } finally {
      setIsLoading(false);
    }
  };

  return { postData, response, isLoading, error };
};
