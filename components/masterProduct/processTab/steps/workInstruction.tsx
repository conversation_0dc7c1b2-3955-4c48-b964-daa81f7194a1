import { ArrowRight } from 'lucide-react';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import DeleteIcon from '@/assets/outline/delete';
import PrimaryButton from '@/components/common/button/primaryButton';
import { Dialog } from '@/components/common/dialog';
import DeleteModal from '@/components/common/modals/deleteModal';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { usePut } from '@/hooks/usePut';

import AddWorkInstructionModal from './workinstruction/addWorkInstructionModal';
import WorkInstructionAccordion, {
    ProcessFlowOption, ValidationRule, WorkInstructionData
} from './workinstruction/workInstructionAccordion';

// API response interfaces
interface FlowCondition {
  // Define flow condition properties if needed
  condition: string;
  next_step: string;
}

interface Rule {
  id?: string;
  type?: string;
  error_message?: string;
  condition?: string;
  flow_conditions?: FlowCondition[];
}

interface WorkInstructionApiResponse {
  work_instructions: {
    id: string;
    instruction_name: string;
    description: string;
    step_id: string;
    step_name: string;
    step_type: string;
    sequence_no: number;
    requires_evidence?: boolean;
    requires_approval?: boolean;
    rules: Rule[];
  }[];
}

// Legacy interface kept for compatibility with AddInstructionModal
interface WorkInstructionItem {
  id: string;
  title: string;
  description: string;
  expanded?: boolean;
}

interface WorkInstructionProps {
  stepId?: string;
  step?: {
    id: string;
    step_name?: string;
  };
  allSteps?: {
    id: string;
    step_name?: string;
  }[];
  onSave?: (data: WorkInstructionState) => void;
  onDelete?: () => void;
}

interface WorkInstructionState {
  enhancedInstructions: WorkInstructionData[];
  workInstructions: WorkInstructionItem[];
}

// Create a store to persist data across component unmounts
const workInstructionStore: Record<string, WorkInstructionState> = {};

const WorkInstruction: React.FC<WorkInstructionProps> = ({
  stepId = 'default',
  step,
  allSteps = [],
  onSave,
  onDelete,
}) => {
  const router = useRouter();
  const { productId } = router.query;
  const accessToken = useAuthStore((state) => state.accessToken);

  // Initialize state from store or with defaults
  const [enhancedInstructions, setEnhancedInstructions] = useState<
    WorkInstructionData[]
  >(() => {
    return workInstructionStore[stepId]?.enhancedInstructions || [];
  });

  // Local state
  const [workInstructions] = useState<WorkInstructionItem[]>(() => {
    return workInstructionStore[stepId]?.workInstructions || [];
  });

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [instructionToDelete, setInstructionToDelete] = useState<string | null>(
    null,
  );
  const [editedInstructions, setEditedInstructions] = useState<Set<string>>(
    new Set(),
  );

  // Initialize the DELETE hook
  const {
    deleteData,
    isLoading: isDeleteLoading,
    response: deleteResponse,
    error: deleteError,
  } = useDelete();

  // Initialize the PUT hook for updates
  const {
    putData,
    isLoading: isPutLoading,
    response: putResponse,
    error: putError,
  } = usePut();

  // Initialize the PUT hook for reorder API
  const {
    putData: reorderInstruction,
    response: reorderResponse,
    isLoading: isReordering,
    error: reorderError,
  } = usePut();

  // Drag and drop state
  const [draggingIndex, setDraggingIndex] = useState<number | null>(null);
  const [isHovering, setIsHovering] = useState<number | null>(null);

  // Fetch work instructions from API
  const { data: workInstructionsData, reFetch: refetchWorkInstructions } =
    useFetch<WorkInstructionApiResponse>(
      accessToken,
      step && productId
        ? `products/${productId}/process_step/${step.id}/work_instructions`
        : undefined,
    );

  // Handle reorder API response
  useEffect(() => {
    if (reorderResponse) {
      console.log('Work instruction reordered successfully:', reorderResponse);
      toast.success('Work instruction order updated successfully');
    }
    if (reorderError) {
      console.error('Error reordering work instruction:', reorderError);
      toast.error('Failed to update work instruction order');
      // Revert the local state by refetching
      refetchWorkInstructions();
    }
  }, [reorderResponse, reorderError]);

  // Drag and drop handlers
  const handleDragStart = (index: number) => {
    console.log('🎯 Work instruction drag started at index:', index);
    setDraggingIndex(index);
  };

  const handleDragOver = (
    e: React.DragEvent<HTMLDivElement>,
    index: number,
  ) => {
    e.preventDefault();

    if (draggingIndex === null || draggingIndex === index) {
      return;
    }

    console.log(
      '🔄 Work instruction drag over - from index:',
      draggingIndex,
      'to index:',
      index,
    );

    const newInstructions = [...enhancedInstructions];
    const draggedItem = newInstructions[draggingIndex];

    // Remove the dragged item
    newInstructions.splice(draggingIndex, 1);
    // Insert it at the new position
    newInstructions.splice(index, 0, draggedItem);

    console.log(
      '📋 Updated work instructions:',
      newInstructions.map((inst, i) => ({
        id: inst.id,
        title: inst.title,
        index: i,
      })),
    );

    setEnhancedInstructions(newInstructions);
    setDraggingIndex(index);
  };

  const handleDragEnd = async () => {
    console.log(
      '🔄 Work instruction drag ended, draggingIndex:',
      draggingIndex,
    );

    if (draggingIndex !== null) {
      const draggedInstruction = enhancedInstructions[draggingIndex];
      console.log('📦 Dragged work instruction:', draggedInstruction);

      // Find the current position of the dragged instruction in the updated array
      const currentIndex = enhancedInstructions.findIndex(
        (inst) => inst.id === draggedInstruction.id,
      );
      const newSequenceNo = currentIndex; // sequence_no is 1-based

      console.log(
        '📍 Current index:',
        currentIndex,
        'New sequence no:',
        newSequenceNo,
      );

      // Only call API if the position actually changed
      const originalInstruction = workInstructionsData?.work_instructions?.find(
        (inst) => inst.id === draggedInstruction.id,
      );

      console.log('🔍 Original instruction:', originalInstruction);
      console.log(
        '🔄 Original sequence_no:',
        originalInstruction?.sequence_no,
        'New sequence_no:',
        newSequenceNo,
      );

      if (
        originalInstruction &&
        originalInstruction.sequence_no !== newSequenceNo + 1
      ) {
        console.log('🚀 Calling work instruction reorder API...');
        try {
          await reorderInstruction(
            accessToken as string,
            `products/process_step/${step?.id}/work_instruction/reorder`,
            {
              instruction_id: draggedInstruction.id,
              new_sequence_no: newSequenceNo,
            },
          );
          console.log('✅ Work instruction reorder API called successfully');
        } catch (error) {
          console.error('❌ Error reordering work instruction:', error);
        }
      } else {
        console.log(
          '⏭️ No API call needed - position unchanged or no original instruction found',
        );
      }
    } else {
      console.log('⚠️ No dragging index found');
    }
    setDraggingIndex(null);
  };

  // Convert API data to component format
  useEffect(() => {
    if (workInstructionsData?.work_instructions) {
      console.log('Work instructions loaded:', workInstructionsData);

      // Map API data to component format
      const mappedInstructions = workInstructionsData.work_instructions.map(
        (instruction) => {
          // Convert API rules to component validation rules
          const validationRules: ValidationRule[] = instruction.rules.map(
            (rule, index) => {
              // Map API rule type to UI rule type
              let uiRuleType = 'equals';
              if (rule.type === 'ValueMatch') uiRuleType = 'equals';
              else if (rule.type === 'ConditionalStep') uiRuleType = 'oneOf';
              else if (rule.type === 'ValueRange') uiRuleType = 'range';

              // Create base rule
              const baseRule: ValidationRule = {
                id: `rule-${instruction.id}-${index}`,
                type: uiRuleType,
                value: rule.condition || '',
                errorMessage: rule.error_message || '',
                originalId: rule.id, // Store the original rule ID from the API
              };

              // Handle ConditionalStep type (oneOf)
              if (
                rule.type === 'ConditionalStep' &&
                rule.flow_conditions &&
                rule.flow_conditions.length > 0
              ) {
                // Extract options from flow conditions
                const options = rule.flow_conditions.map((fc) => fc.condition);

                // Create process flow options
                const processFlowOptions = rule.flow_conditions.map((fc) => ({
                  value: fc.condition,
                  nextStep: fc.next_step,
                }));

                return {
                  ...baseRule,
                  options,
                  processFlowOptions,
                };
              }

              // For ValueRange type, the condition is already in the correct format (min-max)
              // We just use it directly as the value

              return baseRule;
            },
          );

          return {
            id: instruction.id,
            title: instruction.instruction_name,
            description: instruction.description,
            requiresEvidence: !!instruction.requires_evidence, // Convert to boolean
            requiresApproval: !!instruction.requires_approval, // Convert to boolean
            validationRules: validationRules,
          };
        },
      );

      setEnhancedInstructions(mappedInstructions);
    }
  }, [workInstructionsData]);

  // Save to store whenever state changes
  useEffect(() => {
    workInstructionStore[stepId] = {
      enhancedInstructions,
      workInstructions,
    };
  }, [enhancedInstructions, workInstructions, stepId]);

  const handleAddEnhancedInstruction = () => {
    // Open the add work instruction modal
    setIsAddModalOpen(true);
  };

  // Handle successful addition of work instruction
  const handleWorkInstructionAdded = () => {
    // Refresh the instructions list from the API
    if (refetchWorkInstructions) {
      refetchWorkInstructions();
    }
  };

  const handleUpdateEnhancedInstruction = (
    instruction: WorkInstructionData,
  ) => {
    setEnhancedInstructions(
      enhancedInstructions.map((item) =>
        item.id === instruction.id ? instruction : item,
      ),
    );

    // Track which instructions have been edited
    setEditedInstructions((prev) => {
      const newSet = new Set(prev);
      newSet.add(instruction.id);
      return newSet;
    });
  };

  // Handle rule field changes, with special handling for type changes
  const handleRuleFieldChange = (
    instructionId: string,
    ruleId: string,
    field: keyof ValidationRule,
    value: string | string[] | ProcessFlowOption[],
  ) => {
    const instruction = enhancedInstructions.find(
      (item) => item.id === instructionId,
    );
    if (instruction) {
      // Find the rule being updated
      const ruleToUpdate = instruction.validationRules.find(
        (rule) => rule.id === ruleId,
      );

      // Special handling for type changes
      if (field === 'type' && typeof value === 'string' && ruleToUpdate) {
        const oldType = ruleToUpdate.type;
        const newType = value;

        // Create updated rule with new type
        const updatedRule: ValidationRule = {
          ...ruleToUpdate,
          type: newType,
        };

        // Initialize type-specific properties
        if (newType === 'range' && oldType !== 'range') {
          // Initialize range value with a default range
          updatedRule.value = '0-100';
        } else if (newType === 'oneOf' && oldType !== 'oneOf') {
          // Initialize conditional step properties
          updatedRule.processFlowOptions = [{ value: '', nextStep: '' }];
          updatedRule.value = ''; // Clear the value field
        } else if (newType === 'equals' && oldType !== 'equals') {
          // Clear type-specific properties
          delete updatedRule.processFlowOptions;
          // Keep the value field but clear it if it was a range format
          if (updatedRule.value && updatedRule.value.includes('-')) {
            updatedRule.value = '';
          }
        }

        // Update the rules array
        const updatedRules = instruction.validationRules.map((rule) =>
          rule.id === ruleId ? updatedRule : rule,
        );

        handleUpdateEnhancedInstruction({
          ...instruction,
          validationRules: updatedRules,
        });
      } else {
        // Normal field update
        const updatedRules = instruction.validationRules.map((rule) =>
          rule.id === ruleId ? { ...rule, [field]: value } : rule,
        );

        handleUpdateEnhancedInstruction({
          ...instruction,
          validationRules: updatedRules,
        });
      }
    }
  };

  // Handle API response for update
  useEffect(() => {
    if (putResponse) {
      toast.success('Work instructions updated successfully');
      refetchWorkInstructions();
      // Clear the edited instructions set after successful update
      setEditedInstructions(new Set());
    }
  }, [putResponse]);

  // Handle API error for update
  useEffect(() => {
    if (putError) {
      toast.error('Failed to update work instructions');
      console.error('Error updating work instructions:', putError);
    }
  }, [putError]);

  // Handle API response for delete
  useEffect(() => {
    if (deleteResponse) {
      toast.success('Work instruction deleted successfully');
      refetchWorkInstructions();
      setIsDeleteModalOpen(false);
      setInstructionToDelete(null);
    }
  }, [deleteResponse]);

  // Handle API error for delete
  useEffect(() => {
    if (deleteError) {
      toast.error('Failed to delete work instruction');
      console.error('Error deleting work instruction:', deleteError);
      setIsDeleteModalOpen(false);
    }
  }, [deleteError]);

  // Show delete confirmation modal
  const handleDeleteEnhancedInstruction = (id: string) => {
    setInstructionToDelete(id);
    setIsDeleteModalOpen(true);
  };

  // Execute the delete API call
  const confirmDeleteInstruction = () => {
    if (instructionToDelete && step && productId) {
      deleteData(
        accessToken as string,
        `products/${productId}/process_step/${step.id}/work_instruction/${instructionToDelete}`,
      );
    }
  };

  const handleAddRule = (instructionId: string) => {
    const instruction = enhancedInstructions.find(
      (item) => item.id === instructionId,
    );
    if (instruction) {
      // Create a base rule with default type 'equals' (ValueMatch)
      const newRule: ValidationRule = {
        id: `rule-${Date.now()}`,
        type: 'equals',
        value: '',
        errorMessage: 'Invalid input',
      };

      handleUpdateEnhancedInstruction({
        ...instruction,
        validationRules: [...instruction.validationRules, newRule],
      });

      // Track this instruction as edited
      setEditedInstructions((prev) => {
        const newSet = new Set(prev);
        newSet.add(instructionId);
        return newSet;
      });
    }
  };

  const handleDeleteRule = (instructionId: string, ruleId: string) => {
    const instruction = enhancedInstructions.find(
      (item) => item.id === instructionId,
    );
    if (instruction) {
      handleUpdateEnhancedInstruction({
        ...instruction,
        validationRules: instruction.validationRules.filter(
          (rule) => rule.id !== ruleId,
        ),
      });

      // Track this instruction as edited
      setEditedInstructions((prev) => {
        const newSet = new Set(prev);
        newSet.add(instructionId);
        return newSet;
      });
    }
  };

  // Map UI validation rules to API format
  const mapValidationRulesToApiFormat = (instruction: WorkInstructionData) => {
    return instruction.validationRules.map((rule) => {
      const apiRule: Rule = {
        type: mapRuleTypeToApi(rule.type),
        error_message: rule.errorMessage,
        condition: rule.value,
      };

      // Add rule ID if it exists in the original data
      if (rule.originalId) {
        apiRule.id = rule.originalId;
      }

      // Add flow conditions for ConditionalStep type
      if (
        rule.type === 'oneOf' &&
        rule.processFlowOptions &&
        rule.processFlowOptions.length > 0
      ) {
        apiRule.flow_conditions = rule.processFlowOptions.map((option) => ({
          condition: option.value,
          next_step: option.nextStep,
        }));
      }

      // For range type, the condition is already in the correct format (min-max)
      // No special handling needed as the value is directly used as the condition

      return apiRule;
    });
  };

  // Map UI rule type to API rule type
  const mapRuleTypeToApi = (uiType: string): string => {
    switch (uiType) {
      case 'equals':
        return 'ValueMatch';
      case 'oneOf':
        return 'ConditionalStep';
      case 'range':
        return 'ValueRange';
      default:
        return 'ValueMatch';
    }
  };

  // Update work instructions in the backend
  const updateWorkInstructions = () => {
    if (!step || !productId || editedInstructions.size === 0) return;

    // Only send the edited instructions to the API
    const instructionsToUpdate = enhancedInstructions
      .filter((instruction) => editedInstructions.has(instruction.id))
      .map((instruction) => ({
        id: instruction.id,
        instruction_name: instruction.title,
        description: instruction.description,
        requires_evidence: instruction.requiresEvidence,
        requires_approval: instruction.requiresApproval || false,
        rules: mapValidationRulesToApiFormat(instruction),
      }));

    if (instructionsToUpdate.length > 0) {
      putData(
        accessToken as string,
        `products/${productId}/process_step/${step.id}/work_instructions`,
        instructionsToUpdate as unknown as Record<string, unknown>,
      );
    }
  };

  const handleSaveAndNext = () => {
    console.log('Save and next clicked');

    // Update work instructions in the backend
    updateWorkInstructions();

    // Save current state to store
    workInstructionStore[stepId] = {
      enhancedInstructions,
      workInstructions,
    };

    // Call onSave callback if provided
    if (onSave) {
      onSave({
        enhancedInstructions,
        workInstructions,
      });
    }
  };

  return (
    <div className="pt-6 pb-0 rounded-lg border border-white-300 h-[35.5rem] flex flex-col justify-between">
      <div className="space-y-6 px-6 flex-grow overflow-y-auto">
        <WorkInstructionAccordion
          instructions={enhancedInstructions}
          onAddInstruction={handleAddEnhancedInstruction}
          onUpdateInstruction={handleUpdateEnhancedInstruction}
          onDeleteInstruction={handleDeleteEnhancedInstruction}
          onAddRule={handleAddRule}
          onDeleteRule={handleDeleteRule}
          handleRuleChange={handleRuleFieldChange}
          allSteps={allSteps}
          currentStepId={step?.id}
          className="mb-6"
          // Drag and drop props
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragEnd={handleDragEnd}
          draggingIndex={draggingIndex}
          isHovering={isHovering}
          setIsHovering={setIsHovering}
        />

        {/* New Add Work Instruction Modal with API Integration */}
        {step && (
          <AddWorkInstructionModal
            open={isAddModalOpen}
            onOpenChange={setIsAddModalOpen}
            stepId={step.id}
            onSuccess={handleWorkInstructionAdded}
          />
        )}
      </div>

      <div className="flex justify-between py-4 px-6 shadow-[0px_0px_1px_0px_#3031330D,_0px_-4px_16px_0px_#30313314]">
        <div
          className="h-10 w-10 flex justify-center items-center bg-white-200 rounded-full cursor-pointer"
          onClick={onDelete}
        >
          <DeleteIcon height="24" width="24" />
        </div>

        <PrimaryButton
          size="medium"
          text={isPutLoading ? 'Saving...' : 'Save'}
          icon={<ArrowRight size={20} />}
          iconPosition="right"
          onClick={handleSaveAndNext}
          disabled={isPutLoading}
        />
      </div>

      {/* Delete Confirmation Modal */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DeleteModal
          title="Delete Work Instruction"
          infoText="Are you sure you want to delete this work instruction? This action cannot be undone."
          btnText="Delete"
          btnLoading={isDeleteLoading}
          onClick={confirmDeleteInstruction}
        />
      </Dialog>
    </div>
  );
};

export default WorkInstruction;
