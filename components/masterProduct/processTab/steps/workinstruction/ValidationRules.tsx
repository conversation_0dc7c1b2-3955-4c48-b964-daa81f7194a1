import { Plus, Trash2 } from 'lucide-react';
import React from 'react';

import LinkButton from '@/components/common/button/linkButton';
import PrimaryButton from '@/components/common/button/primaryButton';
import TertiaryButton from '@/components/common/button/tertiaryButton';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';

import { ProcessFlowOption, ValidationRule } from './workInstructionAccordion';

interface ValidationRulesProps {
  instructionId: string;
  validationRules: ValidationRule[];
  onAddRule: (instructionId: string) => void;
  onDeleteRule: (instructionId: string, ruleId: string) => void;
  handleRuleChange: (
    instructionId: string,
    ruleId: string,
    field: keyof ValidationRule,
    value: string | string[] | ProcessFlowOption[],
  ) => void;
  allSteps?: {
    id: string;
    step_name?: string;
  }[];
  currentStepId?: string;
}

const ValidationRules: React.FC<ValidationRulesProps> = ({
  instructionId,
  validationRules,
  onAddRule,
  onDeleteRule,
  handleRuleChange,
  allSteps = [],
  currentStepId,
}) => {
  console.log(validationRules);
  return (
    <div className="mt-6">
      <div className="flex items-center justify-between mb-5">
        <div>
          <h3 className="text-base font-medium text-dark-100">
            Validation Rules
          </h3>
          <p className="text-sm text-grey-300 mt-1">
            Set the required rules for this work instruction
          </p>
        </div>
        <TertiaryButton
          size="medium"
          text="Add Rule"
          icon={<Plus size={16} />}
          iconPosition="left"
          onClick={() => onAddRule(instructionId)}
        />
      </div>

      {/* Rules list */}
      {validationRules.map((rule, ruleIdx) => (
        <div key={rule.id}>
          <div className="mb-5 rounded-lg p-5 bg-white-150">
            <div className="flex items-center justify-between mb-5">
              <div className="font-medium text-dark-300 text-lg">
                Rule {ruleIdx + 1}
              </div>
              <button
                onClick={() => onDeleteRule(instructionId, rule.id)}
                className="text-grey-300 hover:text-red-500 transition-colors"
                aria-label="Delete rule"
              >
                <Trash2 size={18} />
              </button>
            </div>

            <div className="grid grid-cols-2 gap-6">
              <div>
                <Label
                  htmlFor={`validation-type-${rule.id}`}
                  className="block text-base font-medium text-dark-100 mb-2"
                >
                  Validation Type
                </Label>
                <Select
                  value={rule.type}
                  onValueChange={(value) =>
                    handleRuleChange(instructionId, rule.id, 'type', value)
                  }
                >
                  <SelectTrigger
                    id={`validation-type-${rule.id}`}
                    className="w-full bg-white"
                  >
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="equals">Equals (ValueMatch)</SelectItem>
                    <SelectItem value="oneOf">Conditional Step</SelectItem>
                    <SelectItem value="range">Range (ValueRange)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label
                  htmlFor={`error-message-${rule.id}`}
                  className="block text-base font-medium text-dark-100 mb-2"
                >
                  Error Message
                </Label>
                <Input
                  id={`error-message-${rule.id}`}
                  value={rule.errorMessage}
                  onChange={(e) =>
                    handleRuleChange(
                      instructionId,
                      rule.id,
                      'errorMessage',
                      e.target.value,
                    )
                  }
                  className="w-full"
                  placeholder="Invalid input"
                />
              </div>

              {/* Conditional fields based on validation type */}
              {rule.type === 'equals' && (
                <div className="col-span-2">
                  <Label
                    htmlFor={`expected-value-${rule.id}`}
                    className="block text-base font-medium text-dark-100 mb-2"
                  >
                    Expected Value:
                  </Label>
                  <Input
                    id={`expected-value-${rule.id}`}
                    value={rule.value}
                    onChange={(e) =>
                      handleRuleChange(
                        instructionId,
                        rule.id,
                        'value',
                        e.target.value,
                      )
                    }
                    className="w-full"
                  />
                </div>
              )}

              {rule.type === 'range' && (
                <div className="col-span-2">
                  <Label
                    htmlFor={`expected-value-${rule.id}`}
                    className="block text-base font-medium text-dark-100 mb-2"
                  >
                    Expected Value:
                  </Label>
                  <Input
                    id={`expected-value-${rule.id}`}
                    value={rule.value}
                    onChange={(e) =>
                      handleRuleChange(
                        instructionId,
                        rule.id,
                        'value',
                        e.target.value,
                      )
                    }
                    className="w-full"
                    placeholder="0-100"
                  />
                  <p className="text-xs text-grey-300 mt-1">
                    For range validation, use format: min-max (e.g., 0-100)
                  </p>
                </div>
              )}

              {rule.type === 'oneOf' && (
                <div className="col-span-2 space-y-4 mt-2">
                  <div>
                    <Label className="block text-base font-medium text-dark-100 mb-2">
                      Create Process Flow:
                    </Label>

                    <div className="mt-4">
                      <div className="grid grid-cols-2 gap-6">
                        <div>
                          <Label className="block text-sm text-grey-300 mb-2">
                            Conditions - If value is
                          </Label>
                        </div>
                        <div>
                          <Label className="block text-sm text-grey-300 mb-2">
                            Then go to:
                          </Label>
                        </div>
                      </div>

                      {(rule.processFlowOptions || []).map(
                        (option, optionIdx) => (
                          <div
                            key={optionIdx}
                            className="grid grid-cols-2 gap-6 items-center mb-3"
                          >
                            <div className="relative">
                              <Input
                                value={option.value}
                                onChange={(e) => {
                                  const newOptions = [
                                    ...(rule.processFlowOptions || []),
                                  ];
                                  newOptions[optionIdx].value = e.target.value;
                                  handleRuleChange(
                                    instructionId,
                                    rule.id,
                                    'options',
                                    newOptions,
                                  );
                                }}
                                className="w-full pr-10"
                                placeholder={`<500`}
                              />
                            </div>

                            <div className="flex items-center space-x-2">
                              <Select
                                value={
                                  rule.processFlowOptions?.[optionIdx]
                                    ?.nextStep || ''
                                }
                                onValueChange={(value) => {
                                  const newProcessFlowOptions = [
                                    ...(rule.processFlowOptions || []),
                                  ];

                                  newProcessFlowOptions[optionIdx] = {
                                    value: option.value,
                                    nextStep: value,
                                  };
                                  handleRuleChange(
                                    instructionId,
                                    rule.id,
                                    'processFlowOptions',
                                    newProcessFlowOptions,
                                  );
                                }}
                              >
                                <SelectTrigger className="w-full bg-white">
                                  <SelectValue placeholder="Select next step" />
                                </SelectTrigger>
                                <SelectContent>
                                  {allSteps
                                    .filter((step) => step.id !== currentStepId) // Filter out current step
                                    .map((step) => (
                                      <SelectItem key={step.id} value={step.id}>
                                        {step.step_name || `Step ${step.id}`}
                                      </SelectItem>
                                    ))}
                                  {allSteps.length <= 1 && (
                                    <SelectItem value="no-steps" disabled>
                                      No other steps available
                                    </SelectItem>
                                  )}
                                </SelectContent>
                              </Select>

                              <button
                                onClick={() => {
                                  const newProcessFlowOptions = [
                                    ...(rule.processFlowOptions || []),
                                  ];
                                  newProcessFlowOptions.splice(optionIdx, 1);

                                  handleRuleChange(
                                    instructionId,
                                    rule.id,
                                    'processFlowOptions',
                                    newProcessFlowOptions,
                                  );
                                }}
                                className="text-grey-300 hover:text-red-500 flex-shrink-0"
                                aria-label="Remove option"
                              >
                                <Trash2 size={16} />
                              </button>
                            </div>
                          </div>
                        ),
                      )}

                      <LinkButton
                        onClick={() => {
                          const newOptions = [
                            ...(rule.processFlowOptions || []),
                            { value: '', nextStep: '' },
                          ];
                          handleRuleChange(
                            instructionId,
                            rule.id,
                            'processFlowOptions',
                            newOptions,
                          );
                        }}
                        type="button"
                        icon={<Plus size={16} className="mr-1" />}
                        text="Add conditions"
                        size="medium"
                      ></LinkButton>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ValidationRules;
