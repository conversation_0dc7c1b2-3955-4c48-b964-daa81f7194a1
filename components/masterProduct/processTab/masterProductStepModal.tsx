import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { z } from 'zod';

import PrimaryButton from '@/components/common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import {
  IOption,
  ReactSelectMulti,
} from '@/components/common/multiSelectInput';
import UploadComponent from '@/components/common/uploadComponent';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePut } from '@/hooks/usePut';
import { TAssetData } from '@/interfaces/asset';
import { TMaterialData } from '@/interfaces/material';
import { IAttachment } from '@/interfaces/misc';
import { IUser } from '@/interfaces/user';
import { mapForMultiSelect } from '@/utils/general';

export const createMasterProductStepSchema = z.object({
  step: z.string().nonempty('Document ID is required'),
  materials: z.array(z.string()).min(1, 'At least one process is required'),
  employees: z.array(z.string()).min(1, 'At least one assignee is required'),
  assets: z.array(z.string()).min(1, 'At least one approver is required'),
});

export interface TData {
  materials: IOption[];
  employees: IOption[];
  assets: IOption[];
}

export interface TStepData {
  id: string;
  materials: string;
  employees: string;
  assets: string;
}

const MasterProductStepModal = ({
  open,
  selectedData,
  closeModal,
}: {
  open: boolean;
  closeModal: () => void;
  selectedData: TStepData | null;
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const [data, setData] = useState<TData>({
    materials: [],
    employees: [],
    assets: [],
  });
  const { data: users, isLoading: userLoading } = useFetch<{
    records: IUser[];
  }>(accessToken, `users`);

  const { data: materials, isLoading: materialLoading } = useFetch<{
    records: TMaterialData[];
  }>(accessToken, `materials`);
  const { data: assets, isLoading: assetsLoading } = useFetch<{
    records: TAssetData[];
  }>(accessToken, `assets`);
  const { putData } = usePut();
  const [error, setError] = useState<Record<string, string> | undefined>();
  const router = useRouter();
  const [addedFiles, setAddedFiles] = useState<IAttachment[]>([]);

  const materialsData = materials?.records?.map((e) => ({
    label: e.name,
    value: e.id,
  })) as IOption[];

  const employeeData = users?.records?.map((e) => ({
    label: e.full_name,
    value: e.id,
  })) as IOption[];

  const assetData = assets?.records?.map((e) => ({
    label: e.name,
    value: e.id,
  })) as IOption[];

  const handleSubmit = () => {
    async function fetch() {
      const payload = {
        materials: data.materials.map((option) => option.value),
        employees: data.employees.map((option) => option.value),
        assets: data.assets.map((option) => option.value),
        ...(addedFiles.length > 0 && { attachments: addedFiles }),
      };
      accessToken &&
        (await putData(
          accessToken,
          `products/${router.query.productId}/process-steps/${selectedData?.id}`,
          payload as Record<string, unknown>,
        ));
    }
    fetch().then(closeModal);
  };

  useEffect(() => {
    setData({ materials: [], assets: [], employees: [] });
    if (selectedData) {
      // Split the input string into an array of strings
      setData({
        materials: mapForMultiSelect(selectedData.materials, materialsData),
        assets: mapForMultiSelect(selectedData.assets, assetData),
        employees: mapForMultiSelect(selectedData.employees, employeeData),
      });
    }
  }, [selectedData, users, assets, materials]);

  return (
    <>
      {/* {error && (
        <Notification
          showNotification={showNotification}
          message={"Error occured while updating the employee"}
          color="text-red-400"
          handleClose={() => setShowNotification(false)}
        />
      )} */}

      {/* {response && (
        <Notification
          showNotification={showNotification}
          message={"Employee updated Successfully"}
          color="text-green-600"
          handleClose={() => setShowNotification(false)}
        />
      )} */}

      <DialogContent className="min-w-[60vw] max-h-[90vh] overflow-y-auto overflow-x-hidden">
        <DialogHeader>
          <DialogTitle>Edit Process details</DialogTitle>
        </DialogHeader>
        <div className="mt-2">
          <div className="flex flex-col gap-9">
            <div>
              <div className="p-6.5">
                <div className="mb-2.5 ">
                  <div className="flex flex-col gap-2.5 flex-1">
                    <Label
                      htmlFor="title"
                      className="text-base font-medium leading-6 text-dark-100"
                    >
                      Materials<span className="text-red-200">*</span>
                    </Label>
                    {materialsData && (
                      <ReactSelectMulti
                        options={materialsData}
                        value={data.materials}
                        placeholder="Select materials"
                        onChange={(value) => {
                          setData((pre) => ({
                            ...pre,
                            materials: value as IOption[],
                          }));
                        }}
                        hasError={Boolean(error?.materials)}
                      />
                    )}
                  </div>
                </div>
                <div className="mb-2.5 ">
                  <div className="flex flex-col gap-2.5 flex-1">
                    <Label
                      htmlFor="title"
                      className="text-base font-medium leading-6 text-dark-100"
                    >
                      People<span className="text-red-200">*</span>
                    </Label>
                    {employeeData && (
                      <ReactSelectMulti
                        options={employeeData}
                        placeholder="Select employees"
                        value={data.employees}
                        onChange={(value) => {
                          setData((pre) => ({
                            ...pre,
                            employees: value as IOption[],
                          }));
                        }}
                        hasError={Boolean(error?.employee)}
                      />
                    )}
                  </div>
                </div>
                <div className="mb-2.5">
                  <div className="flex flex-col gap-2.5 flex-1">
                    <Label
                      htmlFor="title"
                      className="text-base font-medium leading-6 text-dark-100 "
                    >
                      Assets<span className="text-red-200">*</span>
                    </Label>
                    {assetData && (
                      <ReactSelectMulti
                        options={assetData}
                        placeholder="Select assets"
                        value={data.assets}
                        onChange={(value) => {
                          setData((pre) => ({
                            ...pre,
                            assets: value as IOption[],
                          }));
                        }}
                        hasError={Boolean(error?.assets)}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <UploadComponent
            addedFiles={addedFiles}
            setAddedFiles={setAddedFiles}
            documentFor="product_hub"
          />
          <div className="flex justify-end mt-0 bg-white mx-0 pl-6 pt-4">
            <PrimaryButton
              size="medium"
              text="Submit"
              onClick={() => {
                handleSubmit();
              }}
            />
          </div>
        </div>
      </DialogContent>
    </>
  );
};

export default MasterProductStepModal;
