import React, { useEffect, useState } from 'react';
import { z } from 'zod';

import PrimaryButton from '@/components/common/button/primaryButton';
import CreatableSingleSelect from '@/components/common/creatableSelect';
import { DialogContent, DialogHeader, DialogTitle } from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import { IOption } from '@/components/common/multiSelectInput';
import {
    Select, SelectContent, SelectItem, SelectTrigger, SelectValue
} from '@/components/common/select';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import useValidators from '@/hooks/useValidator';
// import { validateForm } from "@/hooks/useValidator";
import { TMaterialData } from '@/interfaces/material';
import { ISelectboxData } from '@/pages/inventory';
import { getPrefillSelectData } from '@/utils/getPrefillSelectData';

const valueSchema = z.object(
  {
    id: z.string().optional(),
    name: z.string(),
  },
  { required_error: 'Value is required' },
);

export const createMaterialSchema = {
  material_id: z.string().nonempty('Material ID is required'),
  name: z.string().nonempty('Name is required'),
  type: valueSchema,
  category: valueSchema,
  units: valueSchema,
};

interface IData extends Record<string, unknown> {
  material_id: string;
  name: string;
  type: { id?: string; name: string } | undefined;
  category: { id?: string; name: string } | undefined;
  units: { id?: string; name: string } | undefined;
}

const CreateMaterialModal = ({
  edit,
  materialData,
  setOpenEdit,
  reFetch,
  materialType,
  materialCategories,
  materialUnits,
}: {
  edit?: boolean;
  materialData?: TMaterialData;
  setOpenEdit?: React.Dispatch<React.SetStateAction<boolean>>;
  reFetch?: () => void;
  materialType?: ISelectboxData[];
  materialCategories?: ISelectboxData[];
  materialUnits?: ISelectboxData[];
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const { putData, isLoading: putLoading } = usePut();
  const { postData, isLoading: postLoading } = usePost();

  const [data, setData] = useState<IData>({
    material_id: '',
    name: '',
    category: undefined,
    units: undefined,
    type: undefined,
  });
  const [error, setError] = useState<Record<string, string> | undefined>();
  const { validationErrors, startValidation, reset } = useValidators({
    schemas: createMaterialSchema,
    values: data,
  });

  const handleSubmit = async () => {
    setError(undefined);

    const payload = { ...data };

    if (materialData) {
      if (materialData.type === data.type?.name) {
        delete payload.type; // Remove type from payload if it's the same
      }
      if (materialData.category === data.category?.name) {
        delete payload.category; // Remove category from payload if it's the same
      }
      if (materialData.units === data.units?.name) {
        delete payload.units; // Remove units from payload if it's the same
      }
    }
    if (edit && materialData) {
      payload.id = materialData.id;
    }

    // Proceed with the API call using the updated payload
    const { hasValidationErrors } = await startValidation();
    if (!hasValidationErrors) {
      if (edit && materialData) {
        await putData(
          accessToken as string,
          `materials/${materialData.id}`,
          payload,
        );
      } else {
        accessToken && (await postData(accessToken, 'materials', payload));
      }
      if (setOpenEdit) setOpenEdit(false);
      reFetch && reFetch();
    } else {
      // setError(errors);
    }
  };

  const handleType = (selected: IOption) => {
    if (selected?.__isNew__) {
      setData((prev) => ({ ...prev, type: { name: selected.label } }));
    } else {
      setData((prev) => ({
        ...prev,
        type: { name: selected.label, id: selected.value },
      }));
    }
  };
  const handleCategory = (selected: IOption) => {
    if (selected?.__isNew__) {
      setData((prev) => ({ ...prev, category: { name: selected.label } }));
    } else {
      setData((prev) => ({
        ...prev,
        category: { name: selected.label, id: selected.value },
      }));
    }
  };
  const handleUnits = (selected: IOption) => {
    if (selected?.__isNew__) {
      setData((prev) => ({ ...prev, units: { name: selected.label } }));
    } else {
      setData((prev) => ({
        ...prev,
        units: { name: selected.label, id: selected.value },
      }));
    }
  };

  useEffect(() => {
    if (edit && materialData) {
      setData({
        material_id: materialData?.material_id,
        name: materialData?.name,
        category: getPrefillSelectData(
          materialData.category,
          materialCategories,
        ),
        units: getPrefillSelectData(materialData.units, materialUnits),
        type: getPrefillSelectData(materialData.type, materialType),
      });
    }
  }, [materialData, edit]);

  return (
    <DialogContent className="min-w-[65vw] max-h-[90vh] overflow-y-auto overflow-x-hidden">
      <DialogHeader>
        <DialogTitle> {edit ? 'Edit' : 'Create'} material</DialogTitle>
      </DialogHeader>

      <div className="mt-2">
        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="materialId"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Material ID<span className="text-red-200">*</span>
            </Label>
            <Input
              placeholder="Enter Material ID"
              id="materialId"
              type="text"
              name="material_id"
              defaultValue={data?.material_id}
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, material_id: e.target.value }))
              }
              errorMsg={validationErrors?.material_id[0]}
            />
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="title"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Material Name<span className="text-red-200">*</span>
            </Label>
            <Input
              type="text"
              name="name"
              defaultValue={data?.name}
              placeholder="Enter Material name"
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, name: e.target.value }))
              }
              errorMsg={validationErrors?.name[0]}
            />
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="type"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Type<span className="text-red-200">*</span>
            </Label>

            <Select
              value={JSON.stringify(data.type)}
              onValueChange={(value) => {
                setData((pre) => ({ ...pre, type: JSON.parse(value) }));
              }}
            >
              <SelectTrigger
                className={validationErrors?.type[0] ? 'border-red-200' : ''}
                id="type"
              >
                <SelectValue placeholder="Enter material type" />
              </SelectTrigger>
              <SelectContent>
                {materialType?.map((e, i) => (
                  <SelectItem value={JSON.stringify(e)} key={i}>
                    {e.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors?.type[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.type[0]}
              </div>
            ) : (
              <></>
            )}

            {/* <CreatableSingleSelect
              placeholder="Enter material type"
              onChange={handleType}
              selectedOption={
                data.type &&
                ({ label: data.type.name, value: data.type.id } as IOption)
              }
              endpoint={'materials/types'}
              hasError={validationErrors?.type[0] ? true : false}
            />
            {validationErrors?.type[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                Type is required
              </div>
            ) : (
              <></>
            )} */}
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="category"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Category<span className="text-red-200">*</span>
            </Label>

            <Select
              value={JSON.stringify(data.category)}
              onValueChange={(value) => {
                setData((pre) => ({ ...pre, category: JSON.parse(value) }));
              }}
            >
              <SelectTrigger
                className={
                  validationErrors?.category[0] ? 'border-red-200' : ''
                }
                id="category"
              >
                <SelectValue placeholder="Enter material category" />
              </SelectTrigger>
              <SelectContent>
                {materialCategories?.map((e, i) => (
                  <SelectItem value={JSON.stringify(e)} key={i}>
                    {e.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors?.category[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.category[0]}
              </div>
            ) : (
              <></>
            )}

            {/* <CreatableSingleSelect
              placeholder="Enter material category"
              onChange={handleCategory}
              selectedOption={
                data.category &&
                ({
                  label: data.category.name,
                  value: data.category.id,
                } as IOption)
              }
              endpoint={'materials/categories'}
              hasError={validationErrors?.category[0] ? true : false}
            />
            {validationErrors?.category[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                Category is required
              </div>
            ) : (
              <></>
            )} */}
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="units"
              className="text-base font-medium leading-6 text-dark-100"
            >
              UOM<span className="text-red-200">*</span>
            </Label>

            <Select
              value={JSON.stringify(data.units)}
              onValueChange={(value) => {
                setData((pre) => ({ ...pre, units: JSON.parse(value) }));
              }}
            >
              <SelectTrigger
                className={validationErrors?.units[0] ? 'border-red-200' : ''}
                id="units"
              >
                <SelectValue placeholder="Enter material units" />
              </SelectTrigger>
              <SelectContent>
                {materialUnits?.map((e, i) => (
                  <SelectItem value={JSON.stringify(e)} key={i}>
                    {e.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors?.units[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.units[0]}
              </div>
            ) : (
              <></>
            )}

            {/* <CreatableSingleSelect
              placeholder="Enter material units"
              onChange={handleUnits}
              selectedOption={
                data.units &&
                ({ label: data.units.name, value: data.units.id } as IOption)
              }
              endpoint={'materials/units'}
              hasError={validationErrors?.units[0] ? true : false}
            />
            {validationErrors?.units[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                Unit is required
              </div>
            ) : (
              <></>
            )} */}
          </div>
        </div>

        <div className="flex justify-end mt-6">
          <PrimaryButton
            size="medium"
            text="Submit"
            onClick={handleSubmit}
            isLoading={putLoading || postLoading}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default CreateMaterialModal;
