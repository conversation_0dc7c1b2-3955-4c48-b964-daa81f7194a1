import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { hasAccess } from '@/utils/roleAccessConfig';
import { getValueOrDefault } from '@/utils/table';
import { formatDate } from '@/utils/time';

import useFetch from '../../hooks/useFetch';
import Breadcrumb from '../common/breadcrumb';
import { Dialog } from '../common/dialog';
import SideBarWrapper from '../common/sidebar/layout';
import CommonTable from '../common/table';
import Tabs from '../common/tabs';

import { AuditRecord, IAuditDetails } from '@/interfaces/audit';
import { ChevronDown } from 'lucide-react';
import MenuPopoverContent from '../common/menuPopoverContent';
import { Popover, PopoverTrigger } from '../common/popover';
import CreateAuditByClause from './components/modals/createAuditByClause';
import CreateAuditByStandard from './components/modals/createAuditByStandard';

enum Status {
  All = 'All',
  Ongoing = 'Ongoing audit',
  Upcoming = 'Upcoming audit',
  Closed = 'Closed',
  PastDue = 'Past due',
}

type Filter = {
  status: Status;
};

const AuditHub = () => {
  const [activeTab, setActiveTab] = useState<number>(0);
  const { accessToken, user } = useAuthStore();
  const [filter, setFilter] = useState({});

  const { data, isLoading, reFetch, error } = useFetch<{
    summary: IAuditDetails;
    records: AuditRecord[];
  }>(accessToken, 'audits', filter);

  const [createAuditByStandard, setCreateAuditByStandard] = useState(false);
  const [createAuditByClause, setCreateAuditByClause] = useState(false);

  const router = useRouter();
  const columnDefs: Array<{
    headerName: string;
    field: string;
    sortable: boolean;
    resizable: boolean;
    getQuickFilterText?: (params: { data: AuditRecord }) => string;
    valueFormatter?: (params: { data: AuditRecord }) => string;
    filter?: boolean | string;
    filterValueGetter?: (params: { data: AuditRecord }) => string;
    cellRenderer?: (params: { data: AuditRecord }) => JSX.Element | string;
    minWidth?: number;
    flex?: number;
  }> = [
    {
      headerName: 'Start date',
      field: 'start_date',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params) => {
        return params.data.start_date;
      },
      valueFormatter: (params) =>
        formatDate(getValueOrDefault(params.data, 'start_date'), false),
      filter: false,
    },
    {
      headerName: 'Name',
      field: 'name',
      resizable: true,
      sortable: true,
      getQuickFilterText: (params) => {
        return params.data.name;
      },
      valueFormatter: (params) => getValueOrDefault(params.data, 'name'),
      filter: false,
      minWidth: 350,
      flex: 2,
    },
    {
      headerName: 'Type',
      field: 'audit_type',
      resizable: true,
      sortable: true,
      filter: false,
      getQuickFilterText: (params) => {
        return params.data.audit_type;
      },
      valueFormatter: (params) => getValueOrDefault(params.data, 'audit_type'),
    },
    {
      headerName: 'Auditors',
      field: 'auditors',
      filter: false,
      sortable: false,
      resizable: true,
      valueFormatter: (params: any) => {
        return params?.data?.auditors
          ? params?.data?.auditors?.map((auditor: any) => {
              return ` ${auditor.full_name}`;
            })
          : '-';
      },
      getQuickFilterText: (params: any) => {
        return params?.data?.auditors
          ? params?.data?.auditors?.map((auditor: any) => {
              return ` ${auditor.full_name}`;
            })
          : '-';
      },
    },
    {
      headerName: 'Auditees',
      field: 'auditees',
      filter: false,
      sortable: false,
      resizable: true,
      valueFormatter: (params: any) => {
        return params?.data?.auditees
          ? params?.data?.auditees?.map((auditee: any) => {
              return ` ${auditee.full_name}`;
            })
          : '-';
      },
      getQuickFilterText: (params: any) => {
        return params?.data?.auditees
          ? params?.data?.auditees?.map((auditee: any) => {
              return ` ${auditee.full_name}`;
            })
          : '-';
      },
    },
    {
      headerName: 'Outcome',
      field: 'results',
      filter: false,
      sortable: false,
      resizable: true,
      cellRenderer: (params) => {
        const content = getValueOrDefault(params.data, 'results');
        if (content !== '-')
          return content.split('\n').map((line: string, index: number) => (
            <React.Fragment key={index}>
              {line}
              <br />
            </React.Fragment>
          ));
      },
      valueFormatter: (params) => getValueOrDefault(params.data, 'results'),
    },
    {
      headerName: 'Standards',
      field: 'standards',
      filter: false,
      sortable: false,
      resizable: true,
      valueFormatter: (params: any) => {
        return params?.data?.standards
          ? params?.data?.standards?.map((standard: any) => {
              return ` ${standard.title}`;
            })
          : '-';
      },
      getQuickFilterText: (params: any) => {
        return params?.data?.standards
          ? params?.data?.standards?.map((standard: any) => {
              return ` ${standard.title}`;
            })
          : '-';
      },
    },
  ];

  const breadcrumbData = [
    {
      name: 'Audit Hub',
      link: '/audit',
    },
  ];

  const handleTabChange = (index: number, status: Status) => {
    setActiveTab(index);
    setFilter({ status });
  };

  const tabsData = [
    {
      name: `All`,
      textColor: 'text-dark-100',
      onClick: () => handleTabChange(0, Status.All),
    },
    {
      name: `Ongoing`,
      textColor: 'text-dark-100',
      onClick: () => handleTabChange(1, Status.Ongoing),
    },
    {
      name: `Upcoming`,
      textColor: 'text-dark-100',
      onClick: () => handleTabChange(2, Status.Upcoming),
    },
    {
      name: `Closed`,
      textColor: 'text-dark-100',
      onClick: () => handleTabChange(3, Status.Closed),
    },
    {
      name: `Past due`,
      textColor: 'text-[#E05252]',
      onClick: () => handleTabChange(4, Status.PastDue),
    },
  ];

  const handleClick = (id: string) => {
    router.push(`audit/${id}`);
  };

  useEffect(() => {
    if (filter && Object.keys(filter).length > 0) {
      reFetch();
    }
  }, [filter]);

  if (error) {
    return (
      <div className="rounded-md border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
        {/* @ts-expect-error dssd */}
        <p className="p-4">{error.response?.data?.error}</p>
      </div>
    );
  }

  const menuPopoverData = [
    {
      id: '1',
      label: 'Create Audit by Standard',
      icon: '',
      selected: false,
      access: true,
      onClick: () => {
        setCreateAuditByStandard(true);
      },
    },
    {
      id: '2',
      label: 'Create Audit by Clause',
      icon: '',
      selected: false,
      access: true,
      onClick: () => {
        setCreateAuditByClause(true);
      },
    },
  ];

  return (
    <SideBarWrapper>
      <div className="flex items-start justify-between my-5">
        <div className="flex flex-col">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            Audit Hub
          </div>
        </div>
      </div>
      <div className="mt-5 mb-6">
        <Tabs
          tabsData={tabsData}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
        />
      </div>

      {createAuditByStandard && (
        <Dialog
          open={createAuditByStandard}
          onOpenChange={setCreateAuditByStandard}
        >
          <CreateAuditByStandard setOpenEdit={setCreateAuditByStandard} />
        </Dialog>
      )}

      {createAuditByClause && (
        <Dialog
          open={createAuditByClause}
          onOpenChange={setCreateAuditByClause}
        >
          <CreateAuditByClause setOpenEdit={setCreateAuditByClause} />
        </Dialog>
      )}

      <div className=" mb-5">
        <CommonTable
          data={data}
          isLoading={isLoading}
          // @ts-expect-error dssd
          columnDefs={columnDefs}
          handleRowClick={(e: any) => handleClick(e.data?.id)}
          searchRightSideElement={
            <div className="flex gap-4">
              {hasAccess(AccessActions.CanCreateAudit, user) && (
                <div className="flex gap-4">
                  <Popover>
                    <PopoverTrigger asChild className="cursor-pointer">
                      <div className="flex items-center gap-4 cursor-pointer !px-5 !py-2 hover:bg-primary-500 rounded-lg text-white-100  bg-primary-400 group">
                        <div className="text-lg leading-7 text-white-100 font-medium">
                          Create Audit
                        </div>
                        <div>
                          <ChevronDown className="h-6 w-6 stroke-white " />
                        </div>
                      </div>
                    </PopoverTrigger>

                    <MenuPopoverContent
                      className="mt-1"
                      data={menuPopoverData}
                    />
                  </Popover>
                </div>
              )}
            </div>
          }
        />
      </div>
    </SideBarWrapper>
  );
};

export default AuditHub;
