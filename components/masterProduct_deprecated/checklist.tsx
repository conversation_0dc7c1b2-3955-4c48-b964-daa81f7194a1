import { useRouter } from 'next/router';
import { useState } from 'react';

import PlusIcon from '@/assets/outline/plus';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { hasAccess } from '@/utils/roleAccessConfig';
import { getValueOrDefault } from '@/utils/table';

import { useDelete } from '../../hooks/useDelete';
import useFetch from '../../hooks/useFetch';
import Breadcrumb from '../common/breadcrumb';
import TertiaryButton from '../common/button/tertiaryButton';
import { Dialog } from '../common/dialog';
import Loader from '../common/loader';
import DeleteModal from '../common/modals/deleteModal';
import Layout from '../common/sidebar/layout';
import CommonTable, { ManageCellRenderer } from '../common/table';
import AddInstructionModal from './modals/addInstruction';

const Checklist = () => {
  const user = useAuthStore((state) => state.user);
  const accessToken = useAuthStore((state) => state.accessToken);
  const router = useRouter();
  const [showInstructionModal, setShowInstructionModal] = useState(false);
  const [selectedInstructionData, setSelectedInstructionData] =
    useState<any>(undefined);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { data, reFetch, isLoading } = useFetch<{
    record: {
      checklist_items: any[];
      process_step: { id: string; name: string };
    };
  }>(
    accessToken,
    `products/${router.query.productId}/process-steps/${router.query.stepId}/checklist-item`,
    {},
  );

  const { deleteData, response, error: errorDelete } = useDelete();

  const stepColumns: any = [
    {
      headerName: 'SNo',
      field: 'sequence_no',
      sortable: true,
      getQuickFilterText: (params: any) => {
        return params.value;
      },
      valueFormatter: (params: any) =>
        getValueOrDefault(params.data, 'sequence_no'),
      filter: false,
      maxWidth: 100,
    },
    {
      headerName: 'Work Instruction',
      field: 'description',
      resizable: true,
      sortable: true,
      valueFormatter: (params: any) =>
        getValueOrDefault(params.data, 'description'),
      filter: false,
    },
    {
      headerName: 'Manage',
      field: 'manage',
      sortable: true,
      cellRenderer: (params: any) => (
        <ManageCellRenderer
          rowData={params.data}
          handleEdit={(rowData) => {
            setSelectedInstructionData(rowData);
            setShowInstructionModal(true);
          }}
          handleDelete={(rowData) => {
            setShowDeleteModal(true);
            setSelectedInstructionData(rowData);
          }}
          hideDelete={!hasAccess(AccessActions.CanDeleteSpecificProduct, user)}
        />
      ),
      width: 100,
      pinned: 'right',
      filter: false,
    },
  ];

  const breadcrumbData = [
    {
      name: 'Master Product',
      link: '/master-product',
    },
    {
      name: 'Product View',
      link: `/master-product/${router.query.productId}`,
      query: { tab: 'process' },
    },
    {
      name: 'Process step',
      link: '#',
    },
  ];

  return (
    <Layout>
      {isLoading ? (
        <Loader />
      ) : (
        <div className="flex flex-col flex-1">
          <div className=" my-5">
            <div>
              <Breadcrumb data={breadcrumbData} />
              <div className="flex justify-between items-center">
                <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-2.5">
                  {data?.record.process_step.name}
                </div>
              </div>
            </div>
          </div>

          <Dialog
            open={showInstructionModal}
            onOpenChange={() => {
              setShowInstructionModal(false);
              setSelectedInstructionData(undefined);
            }}
          >
            <AddInstructionModal
              closeModal={() => {
                setShowInstructionModal(false);
                setSelectedInstructionData(undefined);
              }}
              sequenceNumber={
                data?.record?.checklist_items[
                  data?.record?.checklist_items?.length - 1
                ]?.sequence_no
                  ? Number(
                      data?.record?.checklist_items[
                        data?.record?.checklist_items?.length - 1
                      ]?.sequence_no,
                    ) + 1
                  : 1
              }
              instruction={selectedInstructionData}
              reFetch={reFetch}
            />
          </Dialog>
          <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
            <DeleteModal
              title={'Delete the instruction'}
              onClick={async () => {
                try {
                  // Await the delete operation
                  accessToken &&
                    (await deleteData(
                      accessToken,
                      `products/${router.query.productId}/process-steps/${router.query.stepId}/checklist-item/${selectedInstructionData?.id}`,
                    ));

                  // Call reFetch only after the delete operation completes
                  await reFetch();

                  // Reset state after re-fetching
                  setShowDeleteModal(false);
                  setSelectedInstructionData(undefined);
                } catch (error) {
                  console.error('Error during delete operation:', error);
                }
              }}
              infoText={'Are you sure you want to delete this instruction?'}
              btnText={'Delete'}
            >
              <div className="p-2 border flex flex-col gap-4 border-white-300 bg-white-100 px-2.5 py-2 rounded-lg">
                <div className="flex flex-col gap-2">
                  <div className="text-sm font-medium leading-5 text-grey-300">
                    Instruction:
                  </div>
                  <div className="text-base font-medium leading-6 text-dark-300">
                    {selectedInstructionData?.description}
                  </div>
                </div>
              </div>
            </DeleteModal>
          </Dialog>
          <CommonTable
            data={{ records: data?.record?.checklist_items }}
            columnDefs={stepColumns}
            searchRightSideElement={
              <>
                {hasAccess(AccessActions.CanAddOrEditProducts, user) && (
                  <TertiaryButton
                    icon={<PlusIcon />}
                    text="Add instruction"
                    size="medium"
                    onClick={() => setShowInstructionModal(true)}
                  />
                )}
              </>
            }
          />
        </div>
      )}
    </Layout>
  );
};

export default Checklist;
