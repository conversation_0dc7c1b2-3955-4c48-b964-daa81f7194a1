import { PlusIcon } from 'lucide-react';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';

import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { IUser, TCurrentUser } from '@/interfaces/user';
import { getValueOrDefault } from '@/utils/general';

import Breadcrumb from '../common/breadcrumb';
import PrimaryButton from '../common/button/primaryButton';
import { Dialog, DialogTrigger } from '../common/dialog';
import Loader from '../common/loader';
import Layout from '../common/sidebar/layout';
import CommonTable from '../common/table';
import CreateUserModal from './components/createUserModal';

const User = () => {
  const { accessToken } = useAuthStore();
  const router = useRouter();

  const [createUser, setCreateUser] = useState(false);

  const breadcrumbData = [
    {
      name: 'Settings',
      link: '/setting',
    },
    {
      name: 'User management',
      link: '#',
    },
  ];

  const {
    data: users,
    isLoading,
    reFetch,
  } = useFetch<{ records: IUser[] }>(accessToken, `company/users`);

  const handleClick = (id: string) => {
    router.push(`users/${id}`);
  };

  const columnDefs: Array<{
    headerName: string;
    field: string;
    sortable: boolean;
    resizable: boolean;
    getQuickFilterText?: (params: { data: TCurrentUser }) => string;
    valueFormatter?: (params: { data: TCurrentUser }) => string;
    filter?: boolean | string;
    filterValueGetter?: (params: { data: TCurrentUser }) => string;
    cellRenderer?: (params: { data: TCurrentUser }) => JSX.Element | string;
  }> = [
    {
      headerName: 'First Name',
      field: 'first_name',
      resizable: true,
      sortable: true,
      getQuickFilterText: (params) => {
        return params.data.first_name;
      },
      valueFormatter: (params) => getValueOrDefault(params.data, 'first_name'),
      filter: false,
    },
    {
      headerName: 'Last Name',
      field: 'last_name',
      resizable: true,
      sortable: true,
      getQuickFilterText: (params) => {
        return params.data.last_name;
      },
      valueFormatter: (params) => getValueOrDefault(params.data, 'last_name'),
      filter: false,
    },
    {
      headerName: 'Email',
      field: 'email',
      resizable: true,
      sortable: true,
      getQuickFilterText: (params) => {
        return params.data.email;
      },
      valueFormatter: (params) => getValueOrDefault(params.data, 'email'),
      filter: false,
    },
    {
      headerName: 'Active',
      field: 'is_active',
      resizable: false,
      sortable: true,
      filter: false,
      cellRenderer: (params) => (params.data.is_active ? 'Yes' : 'No'),
    },
  ];

  return isLoading ? (
    <Loader className="h-[80vh]" />
  ) : (
    <div>
      <Layout>
        <div className=" my-5">
          <div className="flex flex-col">
            <Breadcrumb data={breadcrumbData} />
            <div className="text-dark-300 font-semibold text-3xl leading-10">
              User management
            </div>
            <div className="flex gap-4">
              {/* {hasAccess(AccessActions.CanCreateAudit, user) && (
						
					)} */}
            </div>
            <div className="mt-5 mb-5">
              <CommonTable
                data={users}
                // @ts-expect-error dssd
                columnDefs={columnDefs}
                handleRowClick={(e: any) => handleClick(e.data?.id)}
                searchRightSideElement={
                  <div className="flex gap-4">
                    <Dialog open={createUser} onOpenChange={setCreateUser}>
                      <DialogTrigger asChild>
                        <PrimaryButton
                          icon={<PlusIcon />}
                          text="Add User"
                          buttonClasses="!px-5 !py-2"
                        />
                      </DialogTrigger>
                      {createUser && (
                        <CreateUserModal
                          reFetch={reFetch}
                          setOpenEdit={setCreateUser}
                        />
                      )}
                    </Dialog>
                  </div>
                }
              />
            </div>
          </div>
        </div>
      </Layout>
    </div>
  );
};

export default User;
