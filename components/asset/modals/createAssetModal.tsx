import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { z } from 'zod';

import PrimaryButton from '@/components/common/button/primaryButton';
import Calendar from '@/components/common/calendar';
import { IOption } from '@/components/common/creatableSelect';
import { DialogContent, DialogHeader, DialogTitle } from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import {
    Select, SelectContent, SelectItem, SelectTrigger, SelectValue
} from '@/components/common/select';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import useValidators from '@/hooks/useValidator';
// import { validateForm } from "@/hooks/useValidator";
import { TAssetData } from '@/interfaces/asset';
import { IUser } from '@/interfaces/user';
import { removeEmptyFields } from '@/utils/removeEmptyFields';
import { getMonthsFromPeriod, getSubscriptionPeriod } from '@/utils/time';

const createAssetSchema = {
  asset_id: z.string().nonempty('Asset ID is required'),
  name: z.string().nonempty('Name is required'),
  description: z.string().nonempty('Description is required'),
  location: z.string().nonempty('Location is required'),
  owner: z.string().nonempty('Owner is required'),
  status: z.string().nonempty('Status is required'),
  calibration: z.boolean(),
  calibration_cert_num: z.string().optional(),
  calibration_period: z.string().optional(),
  calibration_date: z.string().optional(),
};

interface IData extends Record<string, unknown> {
  asset_id: string;
  name: string;
  description: string;
  owner: string;
  location: string;
  purchase_date: string;
  status: string;
  calibration: boolean;
  calibration_date?: string;
  calibration_cert_num?: string;
  calibration_period?: string;
}

const CreateAssetModal = ({
  edit,
  assetData,
  setOpenEdit,
  reFetch,
}: {
  edit?: boolean;
  assetData?: TAssetData;
  setOpenEdit?: React.Dispatch<React.SetStateAction<boolean>>;
  reFetch?: () => void;
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const { data: users, isLoading: userLoading } = useFetch<
    { records: IUser[] },
    { asset_admin: boolean }
  >(accessToken, `users`, {
    asset_admin: true,
  });
  const { putData, response, isLoading } = usePut();
  const {
    postData,
    response: createResponse,
    isLoading: createIsloading,
    error: createError,
  } = usePost();

  const [data, setData] = useState<IData>({
    asset_id: '',
    name: '',
    description: '',
    owner: '',
    location: '',
    purchase_date: '',
    status: '',
    calibration_date: '',
    calibration: true,
    calibration_cert_num: '',
    calibration_period: '',
  });
  const [error, setError] = useState<Record<string, string> | undefined>();
  const { validationErrors, startValidation, reset } = useValidators({
    schemas: createAssetSchema,
    values: data,
  });

  const userData = users?.records?.map((e) => ({
    label: e.full_name,
    value: e.id,
  })) as IOption[];

  const handleSubmit = async () => {
    setError(undefined);
    const { hasValidationErrors } = await startValidation();

    if (!hasValidationErrors) {
      const payload: any = { ...data };

      payload.calibration_period = getMonthsFromPeriod(data.calibration_period);
      if (!payload?.calibration) {
        delete payload.calibration_cert_num;
        delete payload.calibration_date;
        delete payload.calibration_period;
      }
      if (true) {
        if (edit && assetData) {
          await putData(
            accessToken as string,
            `assets/${assetData.id}`,
            removeEmptyFields({ ...payload, id: assetData.id }),
          );
        } else {
          accessToken &&
            (await postData(accessToken, 'assets', removeEmptyFields(payload)));
        }
        if (setOpenEdit) setOpenEdit(false);
        reFetch && reFetch();
      } else {
        // setError(errors);
      }
    }
  };

  useEffect(() => {
    if (edit && assetData) {
      setData({
        asset_id: assetData?.asset_id,
        name: assetData?.name,
        description: assetData?.description,
        owner: assetData?.owner?.id,
        location: assetData?.location,
        purchase_date: assetData?.purchase_date,
        status: assetData?.status,
        calibration_date: assetData?.calibration_date,
        calibration: assetData?.calibration,
        calibration_cert_num: assetData?.calibration_cert_num,
        calibration_period: getSubscriptionPeriod(
          assetData?.calibration_period,
        ),
      });
    }
  }, [assetData, edit]);

  return (
    <DialogContent className="min-w-[65vw] max-h-[90vh] overflow-y-auto overflow-x-hidden">
      <DialogHeader>
        <DialogTitle>{edit ? 'Edit' : 'Create'} Asset</DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="asset_id"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Asset ID<span className="text-red-200">*</span>
            </Label>
            <Input
              placeholder="Enter asset ID"
              id="assetID"
              type="text"
              name="asset_id"
              value={data?.asset_id}
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, asset_id: e.target.value }))
              }
              errorMsg={validationErrors?.asset_id[0]}
            />
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="title"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Asset Name<span className="text-red-200">*</span>
            </Label>
            <Input
              type="text"
              name="name"
              value={data?.name}
              placeholder="Enter Asset name"
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, name: e.target.value }))
              }
              errorMsg={validationErrors?.name[0]}
            />
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="description"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Description<span className="text-red-200">*</span>
            </Label>
            <Input
              placeholder="Enter description"
              id="description"
              type="text"
              name="description"
              value={data?.description}
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, description: e.target.value }))
              }
              errorMsg={validationErrors?.description[0]}
            />
          </div>
        </div>
        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="owner"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Asset Owner<span className="text-red-200">*</span>
            </Label>
            <Select
              value={data.owner}
              onValueChange={(value) => {
                setData((pre) => ({
                  ...pre,
                  owner: value,
                }));
              }}
            >
              <SelectTrigger
                className={validationErrors?.job_title ? 'border-red-200' : ''}
                id="owner"
              >
                <SelectValue placeholder="Enter owner" />
              </SelectTrigger>
              <SelectContent>
                {userData?.map((e, i) => (
                  <SelectItem value={e.value} key={i}>
                    {e.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="location"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Asset Location<span className="text-red-200">*</span>
            </Label>
            <Input
              type="text"
              name="name"
              value={data?.location}
              placeholder="Enter location"
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, location: e.target.value }))
              }
              errorMsg={validationErrors?.location[0]}
            />
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1 relative">
            <Label
              htmlFor="purchase_date"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Purchase Date
            </Label>
            <Calendar
              selectedDate={data?.purchase_date}
              onDateChange={(date) => {
                if (date) {
                  setData((prev) => ({
                    ...prev,
                    purchase_date: moment(date as string).format('YYYY-MM-DD'),
                  }));
                } else {
                  setData((prev) => ({
                    ...prev,
                    purchase_date: '',
                  }));
                }
              }}
              allowPastDates
              // className={'absolute top-0 w-full h-11'}
            />
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="status"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Asset Status<span className="text-red-200">*</span>
            </Label>
            <Select
              value={data.status}
              onValueChange={(value) => {
                setData((pre) => ({ ...pre, status: value }));
              }}
            >
              <SelectTrigger
                className={validationErrors?.status[0] ? 'border-red-200' : ''}
                id="status"
              >
                <SelectValue placeholder="Enter status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="In use">In use</SelectItem>
                <SelectItem value="Maintenance">Maintenance</SelectItem>
                <SelectItem value="Breakdown">Breakdown</SelectItem>
                <SelectItem value="Calibration">Calibration</SelectItem>
              </SelectContent>
            </Select>
            {validationErrors?.status[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.status[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>
        <div className="flex flex-col gap-2.5 flex-1 mb-5">
          <Label
            htmlFor="calibration"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Calibration Required<span className="text-red-200">*</span>
          </Label>
          <Select
            value={data.calibration ? 'True' : 'False'}
            onValueChange={(value) => {
              setData((pre) => ({
                ...pre,
                calibration: value == 'True' ? true : false,
              }));
            }}
          >
            <SelectTrigger
              className={
                validationErrors?.calibration[0] ? 'border-red-200' : ''
              }
              id="calibration"
            >
              <SelectValue placeholder="Select calibration" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="True">Yes</SelectItem>
              <SelectItem value="False">No</SelectItem>
            </SelectContent>
          </Select>
          {validationErrors?.calibration[0] ? (
            <div className="text-xs font-semibold leading-5 text-left text-red-200">
              {validationErrors?.calibration[0]}
            </div>
          ) : (
            <></>
          )}
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col  flex-1">
            <Label
              htmlFor="calibration_date"
              className="text-base font-medium leading-6 text-dark-100 mb-2.5"
            >
              Calibration Date
            </Label>

            <Calendar
              selectedDate={
                data?.calibration_date ? data?.calibration_date : ''
              }
              onDateChange={(date) => {
                if (date) {
                  setData((prev) => ({
                    ...prev,
                    calibration_date: moment(date as string).format(
                      'YYYY-MM-DD',
                    ),
                  }));
                } else {
                  setData((prev) => ({
                    ...prev,
                    calibration_date: '',
                  }));
                }
              }}
              className={
                validationErrors?.calibration_date[0]
                  ? 'border !border-red-200'
                  : ''
              }
              disabled={!data?.calibration}
            />
            {validationErrors?.calibration_date[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.calibration_date[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="calibration_cert_num"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Calibration Certificate No.
            </Label>
            <Input
              placeholder="Enter certificate no."
              id="calibration_cert_num"
              type="text"
              name="calibration_cert_num"
              value={data?.calibration_cert_num}
              required
              onChange={(e) =>
                setData((pre) => ({
                  ...pre,
                  calibration_cert_num: e.target.value,
                }))
              }
              errorMsg={validationErrors?.calibration_cert_num[0]}
              disabled={!data?.calibration}
            />
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="calibration_period"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Calibration Period
            </Label>
            <Select
              value={data.calibration_period}
              onValueChange={(value) => {
                setData((pre) => ({
                  ...pre,
                  calibration_period: value,
                }));
              }}
              disabled={!data?.calibration}
            >
              <SelectTrigger
                className={
                  validationErrors?.calibration_period[0]
                    ? 'border-red-200'
                    : ''
                }
                id="calibration period"
              >
                <SelectValue placeholder="Select calibration period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Monthly">Monthly</SelectItem>
                <SelectItem value="Quarterly">Quarterly</SelectItem>
                <SelectItem value="Half-yearly">Half-yearly</SelectItem>
                <SelectItem value="Yearly">Yearly</SelectItem>
              </SelectContent>
            </Select>
            {validationErrors?.calibration_period[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.calibration_period[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>

        <div className="flex justify-end mt-6">
          <PrimaryButton size="medium" text="Submit" onClick={handleSubmit} />
        </div>
      </div>
    </DialogContent>
  );
};

export default CreateAssetModal;
