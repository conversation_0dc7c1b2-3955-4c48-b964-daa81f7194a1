import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import Logo from '@/assets/bprLogo.png';
import BPRGearLogo from '@/assets/gearLogo';
import BPRLogo from '@/assets/logo';
import ClosePanelIcon from '@/assets/outline/closePanel';
import AssetHubIcon from '@/assets/sidebarIcon/assetHub';
import AuditHubIcon from '@/assets/sidebarIcon/auditHub';
import DocumentHubIcon from '@/assets/sidebarIcon/documentHub';
import ImprovementHubIcon from '@/assets/sidebarIcon/improvementHub';
import InventoryHubIcon from '@/assets/sidebarIcon/inventoryHub';
import MasterHubIcon from '@/assets/sidebarIcon/masterHub';
import PeopleHubIcon from '@/assets/sidebarIcon/peopleHub';
import ProductionHubIcon from '@/assets/sidebarIcon/productionHub';
import RiskHub from '@/assets/sidebarIcon/riskHub';
import StandardHubIcon from '@/assets/sidebarIcon/standardHub';
import VendorHubIcon from '@/assets/sidebarIcon/vendorHub';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { hasAccess } from '@/utils/roleAccessConfig';
import { showMenuItemForCompany } from '@/utils/showMenuItemForCompany';
import { cn } from '@/utils/styleUtils';

const Sidebar = () => {
  const { user } = useAuthStore();
  const [selectedTab, setSelectedTab] = useState('');
  const [expand, setExpand] = useState(true);
  const pathname = usePathname();
  const MenuData = [
    {
      id: 'standard',
      label: 'Standard',
      icon: (
        <StandardHubIcon
          color={selectedTab.includes('/standard') ? '#016366' : '#575757'}
        />
      ),
      link: '/standard',
      access: hasAccess(AccessActions.ShowSidebarStandard, user),
    },
    {
      id: 'document',
      label: 'Document',
      icon: (
        <DocumentHubIcon
          color={selectedTab.includes('/document') ? '#016366' : '#575757'}
        />
      ),
      link: '/document',
      access: hasAccess(AccessActions.ShowSidebarDocument, user),
    },

    {
      id: 'people',
      label: 'People',
      icon: (
        <PeopleHubIcon
          color={selectedTab === '/people' ? '#016366' : '#575757'}
        />
      ),
      link: '/people',
      access: hasAccess(AccessActions.ShowSidebarPeople, user),
    },
    {
      id: 'asset',
      label: 'Asset',
      icon: (
        <AssetHubIcon
          color={selectedTab === '/asset' ? '#016366' : '#575757'}
        />
      ),
      link: '/asset',
      access: hasAccess(AccessActions.ShowSidebarAsset, user),
    },
    {
      id: 'inventory',
      label: 'Inventory',
      icon: (
        <InventoryHubIcon
          color={selectedTab === '/inventory' ? '#016366' : '#575757'}
        />
      ),
      link: '/inventory',
      access: hasAccess(AccessActions.ShowSidebarInventory, user),
    },
    {
      id: 'master',
      label: 'Master Product',
      icon: (
        <MasterHubIcon
          color={selectedTab === '/master' ? '#016366' : '#575757'}
        />
      ),
      link: '/master-product',
      access:
        hasAccess(AccessActions.ShowSidebarMaster, user) &&
        showMenuItemForCompany(user?.company?.id || ''),
    },
    {
      id: 'production',
      label: 'Production',
      icon: (
        <ProductionHubIcon
          color={selectedTab === '/production' ? '#016366' : '#575757'}
        />
      ),
      link: '/production',
      access:
        hasAccess(AccessActions.ShowSidebarProduction, user) &&
        showMenuItemForCompany(user?.company?.id || ''),
    },
    // {
    //   id: 'vendor',
    //   label: 'Vendor',
    //   icon: (
    //     <VendorHubIcon
    //       color={selectedTab === '/test7' ? '#016366' : '#575757'}
    //     />
    //   ),
    //   link: '/test7',
    // },
    // {
    //   id: 'risk',
    //   label: 'Risk',
    //   icon: (
    //     <RiskHub color={selectedTab === '/test8' ? '#016366' : '#575757'} />
    //   ),
    //   link: '/test8',
    // },
    {
      id: 'vendor',
      label: 'Vendor',
      icon: (
        <VendorHubIcon
          color={selectedTab === '/vendor' ? '#016366' : '#575757'}
        />
      ),
      link: '/vendor',
      access: hasAccess(AccessActions.ShowSidebarVendor, user),
    },
    {
      id: 'improvement',
      label: 'Improvement',
      icon: (
        <ImprovementHubIcon
          color={selectedTab === '/improvement' ? '#016366' : '#575757'}
        />
      ),
      link: '/improvement',
      access: hasAccess(AccessActions.ShowSidebarImprovement, user),
    },
    {
      id: 'audit',
      label: 'Audit',
      icon: (
        <AuditHubIcon
          color={selectedTab === '/audit' ? '#016366' : '#575757'}
        />
      ),
      link: '/audit',
      access: hasAccess(AccessActions.ShowSidebarAudit, user),
    },
  ];

  useEffect(() => {
    if (pathname) {
      setSelectedTab(pathname);
    }
  }, [pathname]);

  useEffect(() => {
    if (pathname === '/') {
      setSelectedTab('/standard');
    }
  }, []);

  return (
    <div
      className={cn(
        'w-[16.25rem] h-screen flex flex-col border-r border-grey-100 bg-white-100 px-0 py-8 overflow-hidden transition-all pt-[1.64rem]',
        !expand ? '!w-[6.2rem] !px-2 !py-8' : '',
      )}
    >
      <div
        className={cn(
          'flex items-center justify-between pb-5 border-b mx-5 border-grey-100 mb-5',
          !expand ? 'flex-col border-b-0 mb-5 pb-0' : '',
        )}
      >
        <div
          className={cn(
            !expand ? 'pb-5 mb-5 border-b border-grey-100 ' : 'flex',
          )}
        >
          {expand ? (
            <BPRLogo width={'140'} height="30" />
          ) : (
            <BPRGearLogo width="40" height="40" />
          )}
        </div>
        <div
          className={cn(
            'h-9 w-9 rounded-full bg-white-150 hover:bg-[#91909A29] flex items-center justify-center cursor-pointer transition-colors',
            !expand ? 'rotate-180' : '',
          )}
          onClick={() => setExpand(!expand)}
        >
          <ClosePanelIcon />
        </div>
      </div>
      <div
        className={cn(
          'flex-1 overflow-x-hidden overflow-y-auto flex  flex-col gap-2',
          !expand ? 'gap-1' : 'px-5',
        )}
      >
        {MenuData.map((item) => (
          <MenuLinks
            key={item.id}
            {...item}
            active={selectedTab.includes(item.link)}
            icon={item.icon}
            expand={expand}
            access={item.access || false}
          />
        ))}
      </div>
    </div>
  );
};

interface IMenuLink {
  label: string;
  icon: React.ReactNode;
  link: string;
  active?: boolean;
  expand: boolean;
  access: boolean;
}

const MenuLinks = ({
  label,
  icon,
  link,
  active = false,
  expand,
  access,
}: IMenuLink) => {
  return (
    access && (
      <Link
        className={cn(
          'flex gap-3 cursor-pointer text-base text-dark-300 leading-6 rounded-lg font-medium p-3 hover:bg-white-200 transition-all',
          active ? '!bg-primary-100 font-semibold' : '',
          !expand
            ? `flex-col gap-3 items-center text-xs font-semibold leading-5 text-dark-300 p-2 ${
                active ? 'text-primary-500' : ''
              }`
            : '',
        )}
        href={link}
      >
        <div className="">{icon}</div>
        {label === 'Master Product' ? (expand ? label : 'Master') : label}
        {label !== 'Master Product' && expand ? ' Hub' : ''}
      </Link>
    )
  );
};

export default Sidebar;
