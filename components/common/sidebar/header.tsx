import { ChevronDown } from 'lucide-react';
import { useRouter } from 'next/router';
import React from 'react';

import LogoutIcon from '@/assets/outline/logout';
import OrganizationIcon from '@/assets/outline/organization';
import SettingIcon from '@/assets/outline/settting';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { hasAccess } from '@/utils/roleAccessConfig';

import MenuPopoverContent from '../menuPopoverContent';
import { Popover, PopoverTrigger } from '../popover';
import MockUserSwitcher from './components/MockUserSwitch';

const Header = () => {
  const router = useRouter();
  const user = useAuthStore((state) => state.user);
  const { setIsLoading } = useAuthStore((state) => state);

  const handleLogout = async () => {
    setIsLoading(true);
    window.location.href = '/api/auth/logout';
    router.push('/login');
  };

  const menuPopoverData = [
    {
      id: '1',
      label: 'Settings',
      icon: <SettingIcon height="20" width="20" color="#282828" />,
      selected: false,
      onClick: () => {
        router.push('/setting');
      },
      access: hasAccess(AccessActions.CanCreateUser, user),
    },
    {
      id: '2',
      label: 'Logout',
      icon: <LogoutIcon height="20" width="20" color="#282828" />,
      selected: false,
      onClick: () => {
        handleLogout();
      },
      access: true,
    },
  ];

  return (
    <div className="flex justify-between items-center pb-4 border-b border-grey-100">
      <div className="flex items-center gap-2.5 h-9">
        <OrganizationIcon />
        <span className="font-medium text-2xl leading-9 text-dark-300">
          {user?.company.name}
        </span>
      </div>
      <div className="flex gap-8 items-center">
        {(process.env.NEXT_PUBLIC_ENVIRONMENT === 'dev' ||
          process.env.NEXT_PUBLIC_ENVIRONMENT === 'local') && (
          <MockUserSwitcher />
        )}

        <Popover>
          <PopoverTrigger asChild className="cursor-pointer">
            <div className="flex items-center gap-1 cursor-pointer px-4 py-2 rounded-lg bg-white-100 group border border-grey-100 hover:border-grey-200">
              <div className="font-medium text-dark-200 leading-6 text-base group-hover:text-dark-300">
                {user?.full_name}
              </div>
              <div>
                <ChevronDown className="h-6 w-6 stroke-dark-200 group-hover:stroke-dark-300" />
              </div>
            </div>
          </PopoverTrigger>

          <MenuPopoverContent data={menuPopoverData} />
        </Popover>
      </div>
      {/* <div className="flex flex-col gap-1">
        <div className="font-semibold text-black leading-6 text-base">
          Nathan Perry
        </div>
        <div className="text-grey-200 text-sm leading-5 font-medium">
          <EMAIL>
        </div>
      </div> */}
    </div>
  );
};

export default Header;
