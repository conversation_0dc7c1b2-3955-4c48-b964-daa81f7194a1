import React, { useEffect, useState } from "react";
import { useAuthStore } from "@/globalProvider/authStore";
import { useRouter } from "next/router";
import Header from "./header";
import Sidebar from "./sidebar";

const Layout = ({ children }: { children: React.ReactNode }) => {
  const { accessToken, user, isLoading } = useAuthStore((state) => state);
  const [isAuthChecked, setIsAuthChecked] = useState(false);
  const router = useRouter();

  useEffect(() => {
    if (accessToken && user?.id) {
      // If the user is authenticated, check for stored redirect URL
      const redirectTo = localStorage.getItem("redirectTo");
      if (redirectTo) {
        // Redirect to the stored URL if available
        localStorage.removeItem("redirectTo");
        router.push(redirectTo);
      } else {
        setIsAuthChecked(true);
      }
    } else if (!isLoading) {
      // If not authenticated, store the page URL and redirect to login
      if (router.pathname !== "/login") {
        localStorage.setItem("redirectTo", router.asPath);
      }
      router.push("/login");
    }
  }, [accessToken, user, isLoading, router]);

  if (isLoading || !isAuthChecked) {
    return;
  }

  return (
    <div className="relative flex w-screen h-screen overflow-hidden">
      <Sidebar />

      <div className="flex-1 flex flex-col overflow-x-hidden overflow-y-auto px-5 py-6">
        <Header />
        {children}
      </div>
    </div>
  );
};

export default Layout;
