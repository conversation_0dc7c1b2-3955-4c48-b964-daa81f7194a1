import axios from 'axios';
import { useParams } from 'next/navigation';
import React, { useCallback, useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { z } from 'zod';

import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';

import TertiaryButton from './button/tertiaryButton';
import { Input } from './input';
import ConfirmModal from './modals/confirmModal';
import FileCard from './modals/uploadModal/fileCard';

const acceptFileTypes = {
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
    '.docx',
    '.doc',
  ],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
    '.xlsx',
  ],
  'application/vnd.ms-excel': ['.xls', '.csv'],
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png'],
};

interface IProps {
  isMulti?: boolean;
  setOpenUploadModal: React.Dispatch<React.SetStateAction<boolean>>;
  refetchDocumentData: () => void;
  openUploadModal: boolean;
  currentVersion?: number;
}
const UploadModal = ({
  isMulti = false,
  setOpenUploadModal,
  refetchDocumentData,
  openUploadModal,
  currentVersion,
}: IProps) => {
  const [loading, setLoading] = useState(false);
  const [addedFile, setAddedFile] = useState<File[] | null>(null);
  const [versionNumber, setVersionNumber] = useState<string | null>(null);
  const [versionError, setVersionError] = useState<string | undefined>();
  const onDrop = useCallback((acceptedFiles: File[]) => {
    setAddedFile(acceptedFiles);
  }, []);
  const { accessToken } = useAuthStore();
  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    multiple: isMulti,
    accept: acceptFileTypes,
  });
  const param = useParams();
  const { postData, response, error } = usePost();
  const UploadModalSchema = {
    versionNumber: z
      .string()
      .refine((value) => value > (currentVersion ?? '0'), {
        message: 'Version number must be greater than 0',
      }),
  };

  const sendToApprover = async () => {
    if (currentVersion && Number(currentVersion) >= Number(versionNumber)) {
      setVersionError(
        'Version number must be greater than the current version' +
          ' ie ' +
          versionNumber,
      );
      return;
    }
    setVersionError(undefined);
    setLoading(true);
    const formData = new FormData();
    const baseUrl = process.env.NEXT_PUBLIC_URL;
    const productVersion = process.env.NEXT_PUBLIC_VERSION;
    const url = `${baseUrl}/${productVersion}/file/upload?document_for=document_hub&sub_path=v${versionNumber}`;

    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
        Authorization: `Bearer ${accessToken}`,
      },
      onUploadProgress: () => {
        setLoading(true);
      },
    };
    formData.append('file', addedFile?.[0] as unknown as Blob);
    axios.post(url, formData, config).then((response) => {
      console.log('response', response);
      const body = {
        file_path: response.data.file_path,
        file_extension: response.data.file_ext,
        version: versionNumber,
      };

      async function fetch() {
        await postData(
          accessToken as string,
          `documents/${param.documentId}/send-for-approval`,
          body,
        );
      }
      fetch();
    });
  };

  useEffect(() => {
    if (response) {
      setOpenUploadModal(false);
      refetchDocumentData();
      setLoading(false);
      setVersionError(undefined);
    }
    if (error) {
      setLoading(false);
      setVersionError(undefined);
    }
  }, [response, error]);

  useEffect(() => {
    if (!openUploadModal) {
      setAddedFile(null);
      setVersionNumber(null);
      setLoading(false);
      setVersionError(undefined);
    }
  }, [openUploadModal]);

  return (
    <ConfirmModal
      title={'Upload document'}
      infoText={
        'Editing and saving a document will overwrite your existing draft. '
      }
      btnText={'Send to approver'}
      onClick={() => sendToApprover()}
      btnLoading={loading}
      dialogClass="min-w-[45.438rem]"
      btnDisabled={!(addedFile && addedFile.length > 0 && versionNumber)}
    >
      <div>
        <div className="text-base leading-6 font-medium text-dark-100 mb-2.5">
          Attach file<span className="text-red-200">*</span>
        </div>
        <div>
          <div
            className=" min-h-28 bg-white-100 border border-dashed border-[#C7C7CC] rounded-xl flex items-center justify-center flex-col gap-2 hover:bg-[#F8F8F8] p-2"
            {...getRootProps()}
          >
            {!(addedFile?.length && addedFile?.length > 0) && (
              <div className="text-sm font-medium leading-5 text-[#49474E]">
                Upload or Drag and drop to upload your file
              </div>
            )}

            <input {...getInputProps()} />
            <div className="flex justify-center items-center flex-wrap gap-2">
              {addedFile?.map((file, index) => (
                <FileCard key={index} file={file} setAddedFile={setAddedFile} />
              ))}
            </div>
            <TertiaryButton
              text={
                isMulti
                  ? 'Add files'
                  : addedFile?.length && addedFile?.length > 0
                  ? 'Replace'
                  : 'Upload file'
              }
              size="small"
            />
          </div>
        </div>

        <div className="text-base leading-6 font-medium text-dark-100 mb-2.5 mt-2.5">
          Version<span className="text-red-200">*</span>
        </div>
        <Input
          type="number"
          placeholder="Version Number"
          value={versionNumber || ''}
          onChange={(e) => {
            setVersionNumber(e.target.value);
            setVersionError(undefined);
          }}
          errorMsg={versionError}
        />
      </div>
    </ConfirmModal>
  );
};

export default UploadModal;
