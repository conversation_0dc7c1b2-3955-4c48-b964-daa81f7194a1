/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-quartz.css';

import { PencilIcon, Search, TrashIcon } from 'lucide-react';
import { useRouter } from 'next/router';
import {
  ReactElement,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import {
  ColDef,
  GridApi,
  GridReadyEvent,
  ModuleRegistry,
  PaginationChangedEvent,
  RowDragEndEvent,
  ValueFormatterParams,
} from '@ag-grid-community/core';
import { AgGridReact } from '@ag-grid-community/react';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { MultiFilterModule } from '@ag-grid-enterprise/multi-filter';
import { SetFilterModule } from '@ag-grid-enterprise/set-filter';

import { Input } from './input';

ModuleRegistry.registerModules([
  ClientSideRowModelModule,
  MenuModule,
  MultiFilterModule,
  SetFilterModule,
]);

interface TCustomColDef extends ColDef {
  headerName: string;
  field: string;
  sortable: boolean;
  resizable: boolean;
  getQuickFilterText: (params: unknown | any) => string;
  valueFormatter: (params: ValueFormatterParams) => string;
  filter: boolean;
}

interface CommonTableProps {
  data: any;
  columnDefs: TCustomColDef[];
  searchBox?: boolean;
  handleRowClick?: (e: any) => void;
  paginate?: boolean;
  rowDrag?: boolean;
  handleDrop?: (e: RowDragEndEvent<any, any>) => void;
  searchRightSideElement?: ReactElement;
  height?: number;
  searchPlaceholder?: string;
  isLoading?: boolean;
}

const CommonTable = ({
  data,
  columnDefs,
  searchBox = true,
  handleRowClick,
  paginate = true,
  rowDrag,
  handleDrop,
  searchRightSideElement,
  height,
  searchPlaceholder,
  isLoading = false,
}: CommonTableProps) => {
  const router = useRouter();
  const defaultPageSize = 10;
  const paginationPageSizeSelector = [5, 10, 20, 50, 100];

  const getUrlPageSize = () =>
    router.query.pageSize
      ? parseInt(router.query.pageSize as string, 10)
      : defaultPageSize;
  const effectivePageSize = getUrlPageSize();

  const [initialLoad, setInitialLoad] = useState(true);

  const gridRef = useRef<AgGridReact>(null);
  const gridApiRef = useRef<GridApi | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [isFiltering, setIsFiltering] = useState(false);

  const onFilterTextBoxChanged = useCallback(() => {
    const filterText = (
      document.getElementById('filter-text-box') as HTMLInputElement
    ).value;
    gridRef.current!.api.setGridOption('quickFilterText', filterText);
    setIsFiltering(!!filterText);
    setTimeout(() => {
      const rowCount = gridRef.current!.api.getDisplayedRowCount();
      if (rowCount === 0) {
        gridRef.current!.api.showNoRowsOverlay();
      } else {
        gridRef.current!.api.hideOverlay();
      }
    }, 100);
  }, []);

  const defaultColDef = useMemo<ColDef>(
    () => ({
      flex: 1,
      minWidth: 120,
      filter: 'agTextColumnFilter',
      suppressHeaderMenuButton: true,
      suppressHeaderContextMenu: true,
      cellClass: 'ag-cell-cstm',
    }),
    [],
  );

  const onFirstDataRendered = useCallback(() => {
    if (gridRef.current && containerRef.current) {
      const rowCount = gridRef.current.api?.getDisplayedRowCount();
      const rowHeight = 60;
      const headerHeight = 50;
      const padding = 10;
      const minHeight = 500;
      const maxHeight = 600;
      const calculatedHeight = rowCount * rowHeight + headerHeight + padding;
      const finalHeight = Math.min(
        Math.max(calculatedHeight, minHeight),
        maxHeight,
      );
      containerRef.current.style.height = `${finalHeight + 25}px`;
      gridRef.current.api?.setGridOption('domLayout', 'autoHeight');

      setTimeout(() => {
        const savedPage = Number(router.query.page);
        gridRef.current?.api.paginationGoToPage(savedPage);
      }, 500);
      setTimeout(() => {
        setInitialLoad(false);
      }, 2000);
    }
  }, [router.query]);

  const onGridReady = useCallback(
    (event: GridReadyEvent) => {
      gridApiRef.current = event.api;
      onFirstDataRendered();
    },
    [router],
  );

  const onPaginationChanged = (params: PaginationChangedEvent) => {
    if (!params.api || initialLoad) return;
    const currentPage = params.api.paginationGetCurrentPage();
    const currentPageSize = params.api.paginationGetPageSize();
    if (
      router.query.page === currentPage.toString() &&
      router.query.pageSize === currentPageSize.toString()
    ) {
      return;
    }

    const newQuery = {
      ...router.query,
      page: currentPage.toString(),
      pageSize: currentPageSize.toString(),
    };
    router.push({ pathname: router.pathname, query: newQuery });
  };

  useEffect(() => {
    onFirstDataRendered();
  }, [data]);

  const noDetailsTemplate = `
    <div class="flex items-center justify-center flex-col h-[250px]">
      <div class="mb-3">
        <image src='/zeroStateTable.svg' alt="No Data" width="100" />
      </div>
      <div class="text-base font-medium leading-6 text-grey-200">
        No details added yet
      </div>
    </div>
  `;

  const noResultsTemplate = `
    <div class="flex items-center justify-center flex-col h-[250px]">
      <div class="mb-3 ">
        <image src='/zeroStateTable.svg' alt="No Results" width="100" />
      </div>
      <div class="text-base font-medium leading-6 text-grey-200">
        No results found
      </div>
    </div>
  `;

  return (
    <div
      style={{ width: '100%', height: '100% !important' }}
      className={'ag-theme-quartz !h-full'}
      ref={containerRef}
    >
      {searchBox && (
        <div className="flex items-center justify-between relative mb-5">
          <div>
            <Input
              id="filter-text-box"
              placeholder={
                searchPlaceholder
                  ? searchPlaceholder
                  : ` Search by${columnDefs.map(
                      (column) => ` ${column.headerName}`,
                    )}...`
              }
              className="w-[40vw] bg-white flex-auto rounded-lg border border-grey-100 py-2.5 px-3 text-black outline-none transition focus:border-primary active:border-primary"
              onInput={onFilterTextBoxChanged}
            />
          </div>
          {searchRightSideElement}
        </div>
      )}

      <AgGridReact
        ref={gridRef}
        domLayout="normal"
        className="!min-h-56"
        rowHeight={50}
        rowData={data?.records}
        columnDefs={columnDefs}
        pagination={paginate}
        paginationPageSize={effectivePageSize}
        paginationPageSizeSelector={paginationPageSizeSelector}
        onPaginationChanged={onPaginationChanged}
        defaultColDef={defaultColDef}
        onGridReady={onGridReady}
        onFirstDataRendered={onFirstDataRendered}
        loading={isLoading}
        // rowDragEntireRow={rowDrag} // Uncomment if row dragging is needed.
        onRowClicked={(e) => {
          if (e?.event?.defaultPrevented) return null;
          e.event?.stopImmediatePropagation();
          if (handleRowClick) handleRowClick(e);
        }}
        onRowDragEnd={(e: RowDragEndEvent<any, any>) => {
          if (handleDrop) handleDrop(e);
        }}
        overlayNoRowsTemplate={
          isFiltering ? noResultsTemplate : noDetailsTemplate
        }
        overlayLoadingTemplate={`<div class="flex items-center justify-center bg-white">
          <div class="h-16 w-16 animate-spin rounded-full border-4 border-solid border-primary-400 border-t-transparent"></div>
        </div>`}
      />
    </div>
  );
};

export const ManageCellRenderer = ({
  rowData,
  handleEdit,
  handleDelete,
  hideDelete,
}: {
  rowData: any;
  handleEdit: (rowData: any) => void;
  handleDelete?: (rowData: any) => void;
  hideDelete?: boolean;
}) => {
  return (
    <div className="flex justify-center items-center gap-2 h-[100%]">
      <button
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          handleEdit(rowData);
        }}
        className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
        title="Edit"
      >
        <PencilIcon height={20} />
      </button>
      {!hideDelete && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            if (handleDelete) handleDelete(rowData);
          }}
          className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
          title="Delete"
        >
          <TrashIcon height={20} />
        </button>
      )}
    </div>
  );
};

export default CommonTable;
