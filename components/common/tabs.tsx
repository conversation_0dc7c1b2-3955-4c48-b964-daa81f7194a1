import { motion } from 'framer-motion';
import React, { ReactElement } from 'react';

import { cn } from '@/utils/styleUtils';

interface ITab {
  name: string;
  textColor: string;
  onClick?: () => void;
}

const Tabs = ({
  tabsData,
  activeTab,
  setActiveTab,
  tabRightSideElement,
  tabGroupName = '',
}: {
  tabsData: ITab[];
  activeTab: number;
  setActiveTab: React.Dispatch<React.SetStateAction<number>>;
  tabRightSideElement?: ReactElement;
  tabGroupName?: string;
}) => {
  return (
    <div className="flex items-center justify-between relative">
      <div className="flex items-center">
        {tabsData.map((tab, index) => (
          <div
            key={`${tab.name}-${index}`}
            onClick={() => {
              if (tab.onClick) tab.onClick();
              if (activeTab !== index) setActiveTab(index);
            }}
            className={cn(
              'mx-3 pb-2.5 cursor-pointer relative text-base font-medium leading-6',
              activeTab === index ? 'text-primary-500' : tab.textColor,
            )}
          >
            {tab.name}
            {activeTab === index && (
              <motion.div
                layoutId={`underline-${tabGroupName}`}
                className="absolute bottom-0 left-0 h-0.5 bg-primary-500"
                style={{ width: '100%' }}
                transition={{ type: 'spring', stiffness: 400, damping: 30 }}
              />
            )}
          </div>
        ))}
      </div>
      {tabRightSideElement}
    </div>
  );
};

export default Tabs;
