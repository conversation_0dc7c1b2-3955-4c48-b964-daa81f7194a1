import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { z } from 'zod';

import PrimaryButton from '@/components/common/button/primaryButton';
import Calendar from '@/components/common/calendar';
import CreatableSingleSelect from '@/components/common/creatableSelect';
import {
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import {
  IOption,
  ReactSelectMulti,
} from '@/components/common/multiSelectInput';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import useValidators from '@/hooks/useValidator';
import { TWorkOrder } from '@/interfaces/production';
import { IUser } from '@/interfaces/user';
import { ISelectboxData } from '@/pages/inventory';
import { transformList } from '@/utils/general';
import { getPrefillSelectData } from '@/utils/getPrefillSelectData';

const optionSchema = z.object({
  label: z.string().optional(),
  value: z.string(),
});

export const workOrderDataSchema = {
  order_no: z.string().nonempty('Order number is required'),
  // reference_no: z.string().nonempty('Reference number is required'),
  customer_name: z.string().nonempty('Customer name is required'),
  uom: z.object(
    {
      id: z.string().optional(),
      name: z.string(),
    },
    { message: 'UOM is required' },
  ),
  quantity: z.number().min(1, 'Quantity must be at least 1'),
  priority: z.enum(['Low', 'Medium', 'High'], {
    required_error: 'Priority is required',
  }),
  start_date: z.string().nonempty('Start date is required'),
  delivery_date: z.string().nonempty('Delivery date is required'),
  product: z.string().nonempty('Product is required'),
  managers: z.array(optionSchema).min(1, 'At least one manager is required'),
};

interface IData extends Record<string, unknown> {
  order_no: string;
  reference_no: string;
  customer_name: string;
  uom: { id?: string; name: string } | undefined;
  quantity: number;
  priority: 'Low' | 'Medium' | 'High'; // Enum-like typing for priority
  start_date: string; // ISO 8601 date string
  delivery_date: string; // ISO 8601 date string
  product: string | undefined; // UUID as a string
  managers: IOption[];
}

const AddWOModal = ({
  edit,
  orderData,
  reFetch,
  setOpenEdit,
}: {
  edit: boolean;
  orderData?: TWorkOrder;
  reFetch: any;
  setOpenEdit?: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const { data: users } = useFetch<{ records: IUser[] }>(accessToken, `users`);
  const { putData, response, isLoading: editIsLoading } = usePut();
  const { data: productList } = useFetch<{
    records: { id: string; name: string }[];
  }>(accessToken, 'products', {
    include_detail: false,
  } as any);
  const {
    postData,
    response: createResponse,
    isLoading: createIsloading,
    error: createError,
  } = usePost();
  const [deliveryDateError, setDeliveryDateError] = useState(false);
  const { data: materialUnits } = useFetch<{ records: ISelectboxData[] }>(
    accessToken,
    'materials/units',
  );
  const [data, setData] = useState<IData>({
    order_no: '',
    reference_no: '',
    customer_name: '',
    uom: undefined,
    quantity: 0,
    priority: 'Low',
    start_date: '', // ISO 8601 date string
    delivery_date: '', // ISO 8601 date string
    product: undefined, // UUID as a string
    managers: [],
  });
  const [error, setError] = useState<Record<string, string> | undefined>();

  const userData = users?.records?.map((e) => ({
    label: e.full_name,
    value: e.id,
  })) as IOption[];

  const { validationErrors, startValidation, reset } = useValidators({
    schemas: workOrderDataSchema,
    values: data,
  });
  const handleUnits = (selected: IOption) => {
    if (selected?.__isNew__) {
      setData((prev) => ({ ...prev, uom: { name: selected.label } }));
    } else {
      setData((prev) => ({
        ...prev,
        uom: { name: selected.label, id: selected.value },
      }));
    }
  };

  useEffect(() => {
    if (edit && orderData) {
      setData({
        order_no: orderData?.order_no,
        reference_no: orderData?.reference_no,
        customer_name: orderData?.customer_name,
        uom: getPrefillSelectData(orderData.uom, materialUnits?.records),
        quantity: orderData?.quantity,
        priority: orderData?.priority,
        start_date: orderData?.start_date,
        delivery_date: orderData?.delivery_date,
        product: orderData?.name,
        managers: transformList(orderData.managers),
      });
    }
  }, [orderData, edit, materialUnits]);

  const handleSubmit = async () => {
    setError(undefined);
    const { hasValidationErrors } = await startValidation();
    if (!hasValidationErrors) {
      const payload = {
        ...data,
        product: productList?.records.find(
          (product) => product.name === data.product,
        )?.id,
        uom: data.uom?.name,
        managers: data.managers.map((option) => option.value),
      };
      if (edit && orderData) {
        await putData(
          accessToken as string,
          `production/work-orders/${orderData.id}`,
          payload,
        ).then(() => {
          if (setOpenEdit) setOpenEdit(false);
          if (reFetch) reFetch();
        });
      } else {
        if (accessToken)
          await postData(accessToken, 'production/work-orders', payload).then(
            () => {},
          );
      }
    } else {
      // setError(errors);
    }
  };

  useEffect(() => {
    if (createResponse) {
      if (setOpenEdit) setOpenEdit(false);
      if (reFetch) reFetch();
    }
  }, [createResponse]);

  useEffect(() => {
    if (
      createError &&
      createError.status === 400 &&
      createError.response?.data &&
      typeof createError.response.data === 'object' &&
      'detail' in createError.response.data &&
      createError.response.data.detail ===
        'Delivery date must be after Start date'
    ) {
      setDeliveryDateError(true);
    }
  }, [createError]);

  useEffect(() => {
    if (data.delivery_date && data.start_date) {
      setDeliveryDateError(false);
    }
  }, [data.delivery_date]);

  return (
    <DialogContent className="min-w-[65vw] max-h-[90vh] overflow-y-auto overflow-x-hidden">
      <DialogHeader>
        <DialogTitle>{edit ? 'Edit' : 'Create'} work order</DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="orderNo"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Order no.<span className="text-red-200">*</span>
            </Label>
            <Input
              placeholder="Enter order no."
              id="orderNo"
              type="text"
              name="order_no"
              defaultValue={data?.order_no}
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, order_no: e.target.value }))
              }
              errorMsg={validationErrors?.order_no[0]}
            />
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="reference_no"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Reference no.
            </Label>
            <Input
              type="text"
              name="reference_no"
              defaultValue={data?.reference_no}
              placeholder="Enter Reference no"
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, reference_no: e.target.value }))
              }
              // errorMsg={validationErrors?.reference_no[0]}
            />
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <div className="flex flex-col gap-2.5 flex-1">
              <Label
                htmlFor="product"
                className="text-base font-medium leading-6 text-dark-100"
              >
                Product<span className="text-red-200">*</span>
              </Label>
              <Select
                value={data.product}
                onValueChange={(value) => {
                  setData((pre) => ({
                    ...pre,
                    product: value,
                  }));
                }}
              >
                <SelectTrigger
                  className={
                    validationErrors?.product[0] ? 'border-red-200' : ''
                  }
                  id="status"
                >
                  <SelectValue placeholder="Select product" />
                </SelectTrigger>
                <SelectContent>
                  {productList?.records?.map((product: any) => {
                    return (
                      <SelectItem
                        value={product.name ? product.name : ' '}
                        key={product.id}
                      >
                        {product.name}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
              {validationErrors?.product[0] ? (
                <div className="text-xs font-semibold leading-5 text-left text-red-200">
                  {validationErrors?.product[0]}
                </div>
              ) : (
                <></>
              )}
            </div>
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="customer_name"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Customer name<span className="text-red-200">*</span>
            </Label>
            <Input
              type="text"
              name="customer_name"
              defaultValue={data?.customer_name}
              placeholder="Enter customer name"
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, customer_name: e.target.value }))
              }
              errorMsg={validationErrors?.customer_name[0]}
            />
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="managers"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Managers<span className="text-red-200">*</span>
            </Label>
            <ReactSelectMulti
              options={userData}
              placeholder="Select managers"
              value={data?.managers}
              onChange={(value) => {
                setData((pre) => ({
                  ...pre,
                  managers: value as IOption[],
                }));
              }}
              hasError={Boolean(validationErrors?.managers[0])}
            />
            {validationErrors?.managers[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.managers[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="uom"
              className="text-base font-medium leading-6 text-dark-100"
            >
              UOM<span className="text-red-200">*</span>
            </Label>

            <Select
              value={JSON.stringify(data.uom)}
              onValueChange={(value) => {
                setData((pre) => ({ ...pre, uom: JSON.parse(value) }));
              }}
            >
              <SelectTrigger
                className={validationErrors?.uom[0] ? 'border-red-200' : ''}
                id="uom"
              >
                <SelectValue placeholder="Enter material units" />
              </SelectTrigger>
              <SelectContent>
                {materialUnits?.records?.map((e, i) => (
                  <SelectItem value={JSON.stringify(e)} key={i}>
                    {e.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors?.uom[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.uom[0]}
              </div>
            ) : (
              <></>
            )}

            {/* <CreatableSingleSelect
              placeholder="Enter material units"
              onChange={handleUnits}
              selectedOption={
                data.uom &&
                ({ label: data.uom.name, value: data.uom.id } as IOption)
              }
              endpoint={'materials/units'}
              hasError={Boolean(validationErrors?.uom[0])}
            />
            {validationErrors?.uom[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.uom[0]}
              </div>
            ) : (
              <></>
            )} */}
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="quantity"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Quantity<span className="text-red-200">*</span>
            </Label>
            <Input
              type="number"
              name="quantity"
              value={data?.quantity}
              placeholder="Enter quantity"
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, quantity: Number(e.target.value) }))
              }
              errorMsg={validationErrors?.quantity[0]}
            />
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="priority_level"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Priority level
            </Label>
            <Select
              value={data.priority}
              onValueChange={(value) => {
                setData((pre) => ({
                  ...pre,
                  priority: value as unknown as 'Low' | 'Medium' | 'High',
                }));
              }}
            >
              <SelectTrigger
                className={
                  validationErrors?.priority[0] ? 'border-red-200' : ''
                }
                id="status"
              >
                <SelectValue placeholder="Enter priority level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Low">Low</SelectItem>
                <SelectItem value="Medium">Medium</SelectItem>
                <SelectItem value="High">High</SelectItem>
              </SelectContent>
            </Select>
            {validationErrors?.priority[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.priority[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="start_date"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Expected start date<span className="text-red-200">*</span>
            </Label>
            <Calendar
              selectedDate={data?.start_date}
              onDateChange={(date) => {
                if (date) {
                  setData((prev) => ({
                    ...prev,
                    start_date: moment(date as string).format('YYYY-MM-DD'),
                  }));
                } else {
                  setData((prev) => ({
                    ...prev,
                    start_date: '',
                  }));
                }
              }}
              className={
                validationErrors?.start_date[0] ? 'border !border-red-200' : ''
              }
            />
            {validationErrors?.start_date[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.start_date[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="delivery_date"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Estimated delivery date<span className="text-red-200">*</span>
            </Label>
            <Calendar
              selectedDate={data?.delivery_date}
              onDateChange={(date) => {
                if (date) {
                  setData((prev) => ({
                    ...prev,
                    delivery_date: moment(date as string).format('YYYY-MM-DD'),
                  }));
                } else {
                  setData((prev) => ({
                    ...prev,
                    delivery_date: '',
                  }));
                }
              }}
              className={
                validationErrors?.delivery_date[0] || deliveryDateError
                  ? 'border !border-red-200'
                  : ''
              }
            />
            {validationErrors?.delivery_date[0] || deliveryDateError ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.delivery_date[0] ||
                  (deliveryDateError &&
                    'Delivery date must be greater than start date')}
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>

        <div className="flex justify-end mt-6">
          <PrimaryButton
            size="medium"
            text="Submit"
            onClick={handleSubmit}
            isLoading={createIsloading || editIsLoading}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default AddWOModal;
