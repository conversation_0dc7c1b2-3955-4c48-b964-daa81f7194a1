import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import PrimaryButton from '@/components/common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import { RadioGroup, RadioGroupItem } from '@/components/common/radio-group';
import UploadComponent from '@/components/common/uploadComponent';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import { IAttachment } from '@/interfaces/misc';
import { IWorkInstruction } from '@/interfaces/production';

const UpdateInstructionModal = ({
  closeModal,
  instruction,
  reFetch,
}: {
  closeModal: any;
  instruction: IWorkInstruction;
  reFetch: () => void;
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const router = useRouter();
  const [addedFiles, setAddedFiles] = useState<IAttachment[]>([]);
  const [compliance, setCompliance] = useState(false);
  const [remarks, setRemarks] = useState('');

  const { postData, isLoading: isLoadingPost } = usePost();

  const {
    putData,
    response: responsePut,
    isLoading: isLoadingPut,
  } = usePut<{ id: string }>();

  useEffect(() => {
    if (responsePut && accessToken) {
      addedFiles &&
        postData(accessToken, 'attachments/', {
          attachment_for: 'work_order_step_checklist',
          record_id: responsePut?.id,
          attachments: addedFiles,
        }).then(() => {
          closeModal();
          reFetch();
        });
    }
  }, [responsePut]);

  const handleSubmit = async () => {
    try {
      const body = {
        compliant: compliance,
        remark: remarks,
        ...(addedFiles?.length > 0 && { evidence: addedFiles[0] }),
      };

      accessToken &&
        (await putData(
          accessToken,
          `production/work-orders/${router.query.workOrderId}/process-steps/${router.query.stepId}/checklist-item/${instruction.id}`,
          body,
        ));
    } catch (error) {
      console.error('Error during operation:', error);
    }
  };

  useEffect(() => {
    setCompliance(instruction.compliant ? true : false);
    setRemarks(instruction.remark ? instruction.remark : '');
    setAddedFiles(instruction?.attachments ? instruction?.attachments : []);
  }, [instruction]);

  return (
    <>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit details</DialogTitle>
        </DialogHeader>
        <div className="w-full">
          <label className="mb-1 block text-black dark:text-white">
            Mark as
          </label>
          <div className="flex h-10 ml-2">
            <RadioGroup
              defaultValue={
                instruction?.compliant ? 'compliant' : 'nonCompliant'
              }
              className="flex gap-6"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="compliant"
                  id="compliant"
                  onClick={(e) => {
                    setCompliance(true);
                  }}
                />
                <Label htmlFor="option-one">Compliant</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="nonCompliant"
                  id="nonCompliant"
                  onClick={(e) => {
                    setCompliance(false);
                  }}
                />
                <Label htmlFor="option-two">Not compliant</Label>
              </div>
            </RadioGroup>
          </div>
        </div>
        <div className="flex flex-col gap-2.5 flex-1">
          <Label className="text-base font-medium leading-6 text-dark-100">
            Remarks
          </Label>
          <Input
            placeholder="Enter remarks"
            id="remarks"
            type="text"
            name="remarks"
            value={remarks}
            required
            onChange={(e) => setRemarks(e.target.value)}
          />
        </div>
        <UploadComponent
          setOpenUploadModal={closeModal}
          refetch={reFetch}
          addedFiles={addedFiles}
          setAddedFiles={setAddedFiles}
          documentFor="production_hub"
        />

        <div className="flex justify-end mt-0 bg-white mx-0 pt-4">
          <PrimaryButton
            size="medium"
            text="Submit"
            isLoading={isLoadingPost || isLoadingPut}
            onClick={handleSubmit}
          />
        </div>
      </DialogContent>
    </>
  );
};

export default UpdateInstructionModal;
