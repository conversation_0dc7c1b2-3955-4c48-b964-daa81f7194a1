import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import EditIcon from '@/assets/outline/edit';
import InfoCircle from '@/assets/outline/infoCircle';
import WarningIcon from '@/assets/outline/warning';
import Cancel from '@/assets/solid/cancel';
import Tick from '@/assets/solid/tick';
import { AccessActions } from '@/constants/access';
import { WorkOrderStepActions, WorkOrderStepStatus } from '@/constants/status';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePut } from '@/hooks/usePut';
import { IWorkInstruction, TStep } from '@/interfaces/production';
import { fetchData } from '@/utils/api';
import { getValueOrDefault } from '@/utils/general';
import { hasAccess } from '@/utils/roleAccessConfig';
import { getlastModified } from '@/utils/table';
import { formatDate } from '@/utils/time';

import { useDelete } from '../../hooks/useDelete';
import useFetch from '../../hooks/useFetch';
import Breadcrumb from '../common/breadcrumb';
import PrimaryButton from '../common/button/primaryButton';
import SecondaryButton from '../common/button/secondaryButton';
import TertiaryButton from '../common/button/tertiaryButton';
import { Dialog } from '../common/dialog';
import Loader from '../common/loader';
import DeleteModal from '../common/modals/deleteModal';
import Layout from '../common/sidebar/layout';
import Status from '../common/status';
import CommonTable from '../common/table';
import TableAttachment from '../common/tableAttachment';
import AddNoteModal from './modals/addNote';
import ApproveModal from './modals/approveModal';
import UpdateInstructionModal from './modals/updateWorkInstruction';

const WorkInstructions = () => {
  const { accessToken, user } = useAuthStore();
  const router = useRouter();
  const {
    data: workInstructionData,
    reFetch,
    isLoading,
  } = useFetch<{
    record: { checklist_items: IWorkInstruction[]; step: TStep };
  }>(
    accessToken,
    `production/work-orders/${router.query.workOrderId}/process-steps/${router.query.stepId}/checklist-item`,
    {},
  );
  const { putData } = usePut();
  const { deleteData, response, error: errorDelete } = useDelete();
  const [noteModal, setNoteModal] = useState(false);
  const [updateData, setUpdateData] = useState(false);
  const [instructionData, setInstructionData] = useState<any>(undefined);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedInstruction, setSelectedInstruction] = useState(undefined);
  const [status, setStatus] = useState<string | undefined>(
    workInstructionData?.record?.step?.status
      ? workInstructionData?.record?.step?.status
      : WorkOrderStepStatus.YetToStart,
  );
  const [actionLoading, setActionLoading] = useState(false);
  const [approveModal, setApproveModal] = useState(false);
  const [notifying, setNotifying] = useState(false);

  const getComplianceValue = (obj: any, key: any) => {
    if (
      obj &&
      obj.hasOwnProperty(key) && //checking if key exists
      obj[key] !== null && //checking null
      JSON.stringify(obj[key]) != JSON.stringify('') //checking empty array/string
    ) {
      if (obj[key] == true) {
        return (
          <div className="w-full h-full flex items-center justify-center">
            <Tick />{' '}
          </div>
        );
      } else {
        return (
          <div className="w-full h-full flex items-center justify-center">
            <Cancel />
          </div>
        );
      }
    } else {
      return (
        <div className="w-full h-full flex items-center justify-center">
          <WarningIcon />
        </div>
      );
    }
  };

  const stepColumns: any = [
    {
      headerName: 'SNo',
      field: 'sequence_no',
      sortable: true,
      getQuickFilterText: (params: any) => {
        return params.value;
      },
      valueFormatter: (params: any) =>
        getValueOrDefault(params.data, 'sequence_no'),
      filter: false,
      minWidth: 70,
      cellStyle: () => {
        return {
          display: 'flex',
          alignItems: 'center',
        };
      },
    },
    {
      headerName: 'Work Instruction',
      field: 'description',
      resizable: true,
      cellStyle: () => {
        return {
          color: status == WorkOrderStepStatus.Ongoing ? '#016366' : 'black',
          overflowWrap: 'break-word',
          lineHeight: '24px',
          paddingTop: '10px',
          paddingBottom: '10px',
          cursor: status == WorkOrderStepStatus.Ongoing ? 'pointer' : 'auto',
        };
      },
      onCellClicked: (params: any) => {
        if (status == WorkOrderStepStatus.Ongoing) {
          setUpdateData(true);
          setSelectedInstruction(params.data);
        }
      },
      valueFormatter: (params: any) =>
        getValueOrDefault(params.data, 'description'),
      filter: false,
      minWidth: 300,
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: 'Compliant',
      field: 'compliant',
      resizable: true,
      sortable: false,
      cellRenderer: (params: any) =>
        getComplianceValue(params.data, 'compliant'),
      filter: false,
      cellStyle: (params: any) => {
        return {
          display: 'flex',
          alignItems: 'center',
        };
      },
    },
    {
      headerName: 'Remark',
      field: 'remark',
      resizable: true,
      valueFormatter: (params: any) => getValueOrDefault(params.data, 'remark'),
      filter: false,
      cellStyle: (params: any) => {
        return {
          overflowWrap: 'break-word',
          lineHeight: '24px',
          paddingTop: '10px',
          paddingBottom: '10px',
        };
      },
      sortable: false,
      minWidth: 150,
      // wrapText: true,
      // autoHeight: true,
    },
    {
      headerName: 'Evidence',
      field: 'attachments',
      sortable: false,
      resizable: true,
      minWidth: 140,
      cellRenderer: (params: any) => {
        return <TableAttachment obj={params.data} backendKey={'attachments'} />;
      },
      filter: false,
      cellStyle: () => {
        return {
          display: 'flex',
          alignItems: 'center',
        };
      },
    },
    {
      headerName: 'Timestamp',
      field: 'last_modified_on',
      resizable: true,
      valueFormatter: (params: any) =>
        formatDate(getValueOrDefault(params.data, 'last_modified_on'), true),
      filter: false,
      cellStyle: (params: any) => {
        return {
          display: 'flex',
          alignItems: 'center',
        };
      },
      minWidth: 180,
    },
    {
      headerName: 'Done by',
      field: 'assignees',
      sortable: false,
      resizable: true,
      valueFormatter: (params: any) =>
        getlastModified(params.data, 'last_modified_by'),
      filter: false,
      cellStyle: (params: any) => {
        return {
          display: 'flex',
          alignItems: 'center',
        };
      },
      minWidth: 180,
    },
  ];

  const breadcrumbData = [
    {
      name: 'Production hub',
      link: '/production',
    },
    {
      name: 'Work order',
      link: `/production/${router.query.workOrderId}`,
      query: { tab: 'process' },
    },
    {
      name: 'Process step',
      link: '#',
    },
  ];

  const handleStart = async () => {
    setActionLoading(true);
    accessToken &&
      (await putData(
        accessToken,
        `production/work-orders/${router.query.workOrderId}/process-steps/${workInstructionData?.record?.step?.id}`,
        {
          status: WorkOrderStepStatus.Ongoing,
          // start_datetime: new Date().toLocaleTimeString(),
        },
      ).then(() => {
        setActionLoading(false);
      }));
    setStatus(WorkOrderStepStatus.Ongoing);
  };

  const handleSubmit = async () => {
    setActionLoading(true);

    accessToken &&
      (await putData(
        accessToken,
        `production/work-orders/${router.query.workOrderId}/process-steps/${workInstructionData?.record?.step?.id}`,
        {
          status: WorkOrderStepStatus.PendingApproval,
          action: WorkOrderStepActions.SendForApproval,
        },
      ).then(() => {
        console.log(response);
        setActionLoading(false);
        setStatus(WorkOrderStepStatus.PendingApproval);
      }));
  };

  const handleNotifyManager = async () => {
    const workOrderID = router.query.workOrderId;
    setNotifying(true);

    workOrderID &&
      accessToken &&
      (await fetchData(
        accessToken,
        `production/work-orders/${workOrderID}/process-steps/${workInstructionData?.record?.step?.id}/notify-managers`,
      ).then(() => {
        setNotifying(false);
      }));
  };

  useEffect(() => {
    workInstructionData?.record?.step?.status &&
      setStatus(workInstructionData?.record?.step?.status);
  }, [workInstructionData]);

  console.log(
    hasAccess(
      AccessActions.CanDeleteSpecificWorkOrder,
      user,
      workInstructionData?.record?.step?.approvers?.some(
        (approver) => approver === user?.id,
      )
        ? workInstructionData?.record?.step?.approvers?.some(
            (approver) => approver === user?.id,
          )
        : false,
    ),
    workInstructionData?.record?.step?.approvers?.some(
      (approver) => approver === user?.id,
    ),
  );

  return (
    <Layout>
      <Dialog open={updateData} onOpenChange={setUpdateData}>
        <>
          {selectedInstruction && (
            <UpdateInstructionModal
              closeModal={() => {
                setUpdateData(false);
              }}
              reFetch={reFetch}
              instruction={selectedInstruction}
            />
          )}
        </>
      </Dialog>
      <Dialog open={noteModal} onOpenChange={setNoteModal}>
        <AddNoteModal
          closeModal={() => {
            setNoteModal(false);
          }}
          note={workInstructionData?.record?.step.instruction}
          reFetch={reFetch}
        />
      </Dialog>
      <Dialog open={approveModal} onOpenChange={setApproveModal}>
        <ApproveModal
          openModal={setApproveModal}
          stepId={workInstructionData?.record?.step?.id}
          setStatus={setStatus}
          reFetch={reFetch}
        />
      </Dialog>
      <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <DeleteModal
          title={instructionData?.description}
          onClick={async () => {
            console.log('deleted');
            try {
              // Await the delete operation
              accessToken &&
                (await deleteData(
                  accessToken,
                  `products/${router.query.productId}/process-steps/${router.query.stepId}/checklist-item/${instructionData?.id}`,
                ));

              // Call reFetch only after the delete operation completes
              // await reFetch();

              // Reset state after re-fetching
              setShowDeleteModal(false);
              setInstructionData(undefined);
            } catch (error) {
              console.error('Error during delete operation:', error);
            }
          }}
          infoText={'Are you sure you wantto delete this item?'}
          btnText={'Delete item'}
        />
      </Dialog>
      {isLoading ? (
        <Loader />
      ) : (
        <div className="flex flex-col flex-1">
          <div className=" my-5">
            <div>
              <Breadcrumb data={breadcrumbData} />
              <div className="flex justify-between items-center">
                <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-2.5">
                  {workInstructionData?.record?.step?.name}
                  {status && <Status type={status.toLowerCase()} />}
                </div>

                {workInstructionData?.record?.checklist_items &&
                  workInstructionData?.record?.checklist_items?.length > 0 && (
                    <div className="flex gap-5">
                      {hasAccess(
                        AccessActions.CanEditSpecificWorkOrder,
                        user,
                        workInstructionData?.record?.step?.assignees?.some(
                          (assignee) => assignee === user?.id,
                        ),
                      ) &&
                        status === WorkOrderStepStatus.Ongoing && (
                          <TertiaryButton
                            text="Notify manager"
                            size="medium"
                            onClick={handleNotifyManager}
                            isLoading={notifying}
                          />
                        )}
                      {hasAccess(
                        AccessActions.CanEditSpecificWorkOrder,
                        user,
                        workInstructionData?.record?.step?.assignees?.some(
                          (assignee) => assignee === user?.id,
                        )
                          ? workInstructionData?.record?.step?.assignees?.some(
                              (assignee) => assignee === user?.id,
                            )
                          : false,
                      ) &&
                        status === WorkOrderStepStatus.YetToStart && (
                          <PrimaryButton
                            text="Start"
                            size="medium"
                            onClick={handleStart}
                            disabled={
                              !workInstructionData?.record?.step?.is_enabled
                            }
                            isLoading={actionLoading}
                          />
                        )}
                      {hasAccess(
                        AccessActions.CanEditSpecificWorkOrder,
                        user,
                        workInstructionData?.record?.step?.approvers?.some(
                          (approver) => approver === user?.id,
                        )
                          ? workInstructionData?.record?.step?.approvers?.some(
                              (approver) => approver === user?.id,
                            )
                          : false,
                      ) &&
                        status === WorkOrderStepStatus.PendingApproval && (
                          <PrimaryButton
                            text="Approve"
                            size="medium"
                            onClick={() => {
                              setApproveModal(true);
                            }}
                            disabled={
                              !workInstructionData?.record?.step?.is_enabled
                            }
                            isLoading={actionLoading}
                          />
                        )}
                    </div>
                  )}
              </div>
            </div>
          </div>

          {workInstructionData?.record?.step.instruction && (
            <div className="w-full bg-white-150 pl-4 pr-4 py-3 rounded-lg mb-5">
              <div className="flex gap-1 items-start justify-between">
                <div className="flex gap-2 items-center justify-between text-base leading-6 font-medium text-dark-300">
                  <InfoCircle /> <span>Special note:</span>
                </div>
                {hasAccess(AccessActions.CanDeleteSpecificWorkOrder, user) && (
                  <div
                    className="rounded-full p-2 bg-white-200 cursor-pointer hover:bg-white-300"
                    onClick={() => {
                      setNoteModal(true);
                    }}
                  >
                    <EditIcon width="24px" />
                  </div>
                )}
              </div>
              <div className="text-sm leading-5 text-dark-100 font-medium">
                {workInstructionData?.record?.step.instruction
                  ? workInstructionData?.record?.step.instruction
                  : ``}
              </div>
            </div>
          )}
          <CommonTable
            searchBox={false}
            data={{ records: workInstructionData?.record?.checklist_items }}
            columnDefs={stepColumns}
            paginate={false}
          />

          <div className="flex gap-5 pt-4 justify-end">
            {hasAccess(AccessActions.CanDeleteSpecificWorkOrder, user) &&
              !workInstructionData?.record?.step?.instruction && (
                <SecondaryButton
                  text="Add a special note"
                  size="medium"
                  onClick={() => setNoteModal(true)}
                />
              )}
            {hasAccess(
              AccessActions.CanEditSpecificWorkOrder,
              user,
              workInstructionData?.record?.step?.assignees?.some(
                (assignee) => assignee === user?.id,
              )
                ? workInstructionData?.record?.step?.assignees?.some(
                    (assignee) => assignee === user?.id,
                  )
                : false,
            ) &&
              status === WorkOrderStepStatus.Ongoing && (
                <PrimaryButton
                  text="Submit"
                  size="medium"
                  onClick={handleSubmit}
                  isLoading={actionLoading}
                />
              )}
          </div>
        </div>
      )}
    </Layout>
  );
};

export default WorkInstructions;
