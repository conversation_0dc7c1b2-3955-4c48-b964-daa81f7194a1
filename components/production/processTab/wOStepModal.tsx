import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { z } from 'zod';

import PrimaryButton from '@/components/common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import Loader from '@/components/common/loader';
import {
  IOption,
  ReactSelectMulti,
} from '@/components/common/multiSelectInput';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePut } from '@/hooks/usePut';
import { IUser } from '@/interfaces/user';
import { mapForMultiSelect } from '@/utils/general';

export const createMasterProductStepSchema = z.object({
  step: z.string().nonempty('Document ID is required'),
  approvers: z.array(z.string()).min(1, 'At least one process is required'),
  assignees: z.array(z.string()).min(1, 'At least one assignee is required'),
});

export interface TData {
  approvers: IOption[];
  assignees: IOption[];
}

export interface TStepData {
  id: string;
  approvers: string;
  assignees: string;
}

const WorkOrderStepModal = ({
  open,
  selectedData,
  closeModal,
  selectedColumn,
}: {
  open: boolean;
  closeModal: () => void;
  selectedData: TStepData | null;
  selectedColumn: 'assignees' | 'approvers';
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const [data, setData] = useState<TData>({
    approvers: [],
    assignees: [],
  });
  const { data: users } = useFetch<{ records: IUser[] }>(accessToken, `users`);
  const { putData, isLoading } = usePut();
  const [error, setError] = useState<Record<string, string> | undefined>();
  const router = useRouter();

  const userData = users?.records?.map((e) => ({
    label: e.full_name,
    value: e.id,
  })) as IOption[];

  const handleSubmit = () => {
    async function fetch() {
      const payload = {
        ...(selectedColumn === 'approvers' && {
          approvers: data.approvers.map((option) => option.value),
        }),
        ...(selectedColumn === 'assignees' && {
          assignees: data.assignees.map((option) => option.value),
        }),
      };
      accessToken &&
        (await putData(
          accessToken,
          `production/work-orders/${router.query.workOrderId}/process-steps/${selectedData?.id}`,
          payload as Record<string, unknown>,
        ));
    }
    fetch().then(closeModal);
  };

  useEffect(() => {
    if (selectedData && users) {
      // Split the input string into an array of strings
      setData({
        approvers: mapForMultiSelect(selectedData.approvers, userData),
        assignees: mapForMultiSelect(selectedData.assignees, userData),
      });
    }
  }, [selectedData, users]);

  return (
    <>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update people</DialogTitle>
        </DialogHeader>
        <div className="p-6.5">
          <div className="mb-2.5 ">
            {selectedColumn === 'approvers' && (
              <div className="">
                <Label
                  htmlFor="title"
                  className="text-base font-medium leading-6 text-dark-100"
                >
                  Approvers
                </Label>
                {
                  <div className="mt-2">
                    <ReactSelectMulti
                      options={userData}
                      value={data.approvers}
                      placeholder="Select approvers"
                      onChange={(value) => {
                        setData((pre) => ({
                          ...pre,
                          approvers: value as IOption[],
                        }));
                      }}
                      hasError={Boolean(error?.approvers)}
                    />
                  </div>
                }
              </div>
            )}
          </div>
          <div className="mb-2.5 ">
            {selectedColumn === 'assignees' && (
              <div className="">
                <Label
                  htmlFor="title"
                  className="text-base font-medium leading-6 text-dark-100"
                >
                  People responsible
                </Label>
                {userData ? (
                  <div className="mt-2">
                    <ReactSelectMulti
                      options={userData}
                      placeholder="Select employees"
                      value={data.assignees}
                      onChange={(value) => {
                        setData((pre) => ({
                          ...pre,
                          assignees: value as IOption[],
                        }));
                      }}
                      hasError={Boolean(error?.assignee)}
                    />
                  </div>
                ) : (
                  <Loader className="h-[150px]" />
                )}
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-end mt-0 bg-white mx-0 pt-1">
          <PrimaryButton
            size="medium"
            text="Submit"
            onClick={handleSubmit}
            isLoading={isLoading}
          />
        </div>
      </DialogContent>
    </>
  );
};

export default WorkOrderStepModal;
