import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';

import EditIcon from '@/assets/outline/edit';
import PlusIcon from '@/assets/outline/plus';
import LinkButton from '@/components/common/button/linkButton';
import { Dialog } from '@/components/common/dialog';
import Status from '@/components/common/status';
import CommonTable from '@/components/common/table';
import TableAttachment from '@/components/common/tableAttachment';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { TCurrentUser } from '@/interfaces/user';
import { getValueOrDefault } from '@/utils/general';
import { hasAccess } from '@/utils/roleAccessConfig';
import { formatDate } from '@/utils/time';

import WorkOrderStepModal from './wOStepModal';

interface TProcessData {
  id: string;
  steps: string;
  material?: string[] | [];
  people?: string[] | [];
  asset?: string[] | [];
  assignees?: string;
  approvers?: string;
  remark?: string;
  end_datetime?: string;
  start_datetime?: string;
}

const WorkOrderProcessTable = ({
  data,
  reFetch,
  edit,
}: {
  data: any;
  reFetch: () => void;
  edit?: boolean;
}) => {
  const [showStepModal, setShowStepModal] = useState<boolean>(false); // [showStepModal, settShowModal]
  const [processData, setProcessData] = useState<TProcessData[]>();
  const [selectedData, setSelectedData] = useState<any | null>(null);
  const [selectedColumn, setSelectedColumn] = useState<
    'assignees' | 'approvers'
  >();
  const router = useRouter();
  const { user } = useAuthStore();

  const handleAdd = (rowData: any) => {
    setSelectedData(rowData);
    setShowStepModal(true);
  };

  const getAddButtonIfNovalue = (
    obj: any,
    key: any,
    handleAdd: (rowData: any) => void,
    user: TCurrentUser,
  ) => {
    if (
      obj &&
      obj.hasOwnProperty(key) &&
      obj[key] !== null &&
      obj[key] !== undefined &&
      JSON.stringify(obj[key]) != JSON.stringify('')
    ) {
      return (
        <div>
          {obj[key]}
          {hasAccess(AccessActions.CanEditSpecificWorkOrder, user) && (
            <LinkButton
              text={'Edit'}
              size="medium"
              onClick={(e) => {
                handleAdd(obj);
                e.stopPropagation();
                e.preventDefault();
                setSelectedColumn(key);
              }}
              icon={<EditIcon height="1.2vw" color="#00797D" />}
            />
          )}
        </div>
      );
    } else {
      if (hasAccess(AccessActions.CanEditSpecificWorkOrder, user)) {
        return (
          <LinkButton
            text={'Add'}
            size="medium"
            onClick={(e) => {
              handleAdd(obj);
              e.stopPropagation();
              e.preventDefault();
              setSelectedColumn(key);
            }}
            icon={<PlusIcon height="1.46vw" color="#00797D" />}
          />
        );
      } else return '--';
    }
  };

  const getWorkOrderProcessColumns = useCallback(() => {
    const workOrderColumns: any = [
      {
        headerName: 'Step',
        field: 'steps',
        sortable: true,
        resizable: true,
        filter: 'agMultiColumnFilter',
        getQuickFilterText: (params: any) => {
          return params.value;
        },
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'steps'),
        RowDrag: true,
        onCellClicked: (params: any) => {
          router.push(
            `${router?.query?.workOrderId}/process-step/${params?.data?.id}`,
          );
        },
        cellStyle: () => {
          return {
            cursor: 'pointer',
            color: '#016366',
          };
        },
      },
      {
        headerName: 'People responsible',
        field: 'assignees',
        resizable: true,
        cellRenderer: (params: any) =>
          user &&
          getAddButtonIfNovalue(params.data, 'assignees', handleAdd, user),
        filter: false,
        minWidth: 200,
        wrapText: true,
        autoHeight: true,
        cellStyle: () => {
          return {
            overflowWrap: 'break-word',
            lineHeight: '24px',
            paddingTop: '10px',
            paddingBottom: '10px',
          };
        },
      },
      {
        headerName: 'Approvers',
        field: 'approvers',
        resizable: true,
        cellRenderer: (params: any) =>
          user &&
          getAddButtonIfNovalue(params.data, 'approvers', handleAdd, user),
        filter: false,
        minWidth: 200,
        wrapText: true,
        autoHeight: true,
        cellStyle: () => {
          return {
            overflowWrap: 'break-word',
            lineHeight: '24px',
            paddingTop: '10px',
            paddingBottom: '10px',
          };
        },
      },
      {
        headerName: 'Materials',
        field: 'materials',
        resizable: true,
        minWidth: 150,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'materials'),
        filter: false,
      },
      {
        headerName: 'Assets',
        field: 'assets',
        resizable: true,
        minWidth: 150,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'assets'),
        filter: false,
      },
      {
        headerName: 'Status',
        field: 'status',
        resizable: true,
        cellRenderer: (params: any) => (
          <div className="py-2.5 ">
            <Status type={params?.data?.status?.toLowerCase()} />
          </div>
        ),
        filter: 'agMultiColumnFilter',
        sortable: false,
      },
      {
        headerName: 'Linked documents',
        field: 'attachments',
        resizable: true,
        cellRenderer: (params: any) => {
          return (
            <TableAttachment obj={params.data} backendKey={'attachments'} />
          );
        },
        filter: false,
        sortable: false,
      },
      {
        headerName: 'Start time',
        field: 'start_datetime',
        resizable: true,
        valueFormatter: (params: any) =>
          formatDate(getValueOrDefault(params.data, 'start_datetime'), true),
        filter: false,
      },
      {
        headerName: 'End time',
        field: 'end_datetime',
        resizable: true,
        valueFormatter: (params: any) =>
          formatDate(getValueOrDefault(params.data, 'end_datetime'), true),
        filter: false,
      },
      {
        headerName: 'Remark',
        field: 'remark',
        resizable: true,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'remark'),
        filter: false,
        sortable: false,
      },
    ];

    return workOrderColumns;
  }, [edit]);

  useEffect(() => {
    const steps = data?.record?.steps;
    steps?.sort((a: any, b: any) => a.sequence_no - b.sequence_no);

    setProcessData(
      steps?.map((step: any) => ({
        ...step,
        steps: step.name,
        materials: step?.materials?.map((e: any) => e.name).join(', '),
        employees: step?.employees?.map((e: any) => e.name).join(', '),
        assets: step?.assets?.map((e: any) => e.name).join(', '),
        assignees: step?.assignees?.map((e: any) => e.name).join(', '),
        approvers: step?.approvers?.map((e: any) => e.name).join(', '),
      })),
    );
  }, [data]);

  return (
    <div className="w-full mt-8 mb-10 h-fit overflow-visible">
      <div className="pb-3 font-semibold text-dark-300"> Table view:</div>
      <div className="w-full">
        <CommonTable
          data={{ records: processData }}
          columnDefs={getWorkOrderProcessColumns()}
          paginate={false}
        />
      </div>
      {selectedColumn && (
        <Dialog open={showStepModal} onOpenChange={setShowStepModal}>
          <WorkOrderStepModal
            open={showStepModal}
            selectedData={selectedData}
            closeModal={() => {
              setShowStepModal(false);
              setSelectedData(null);
              reFetch();
            }}
            selectedColumn={selectedColumn}
          />
        </Dialog>
      )}
    </div>
  );
};

export default WorkOrderProcessTable;
