import { Check, Minus } from 'lucide-react';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';

import CheckIcon from '@/assets/outline/check';
import EditIcon from '@/assets/outline/edit';
import UnlinkIcon from '@/assets/outline/unlink';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import { IProcess } from '@/interfaces/process';
import { IDocument, Process } from '@/interfaces/standard';
import { hasAccess } from '@/utils/roleAccessConfig';
import { cn } from '@/utils/styleUtils';
import { getValueOrDefault } from '@/utils/table';

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '../common/accordion';
import { Dialog, DialogTrigger } from '../common/dialog';
import DeleteModal from '../common/modals/deleteModal';
import CommonTable from '../common/table';
import OtpModal from '../document/components/modals/otpModal';
import LinkProcessModal from './standardPage/linkProcessModal';

interface Response {
  id: string;
  step: string;
}

interface IProps {
  canEdit?: boolean;
  process: {
    id: string;
    name: string;
    documents: IDocument[];
  };
  subClause: {
    id: string;
    clause_no: string;
    title: string;
    description: string;
    question: string;
    is_compliant: boolean;
    processes: Process[];
  };
  reFetch: () => void;
  index: number;
  processes: {
    records: IProcess[];
  } | null;
}

const ProcessDocument = ({
  process,
  subClause,
  reFetch,
  index,
  processes,
  canEdit = true,
}: IProps) => {
  const [editProcessModal, setEditProcessModal] = useState(false);
  const [selectedProcess, setSelectedProcess] = useState<string | null>(null);
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const { accessToken, user } = useAuthStore();
  const cfr_enabled = useAuthStore(
    (state) => state.user?.company.is_cfr11_required,
  );
  const router = useRouter();
  const {
    deleteData,
    isLoading: deleteLoading,
    response,
    error,
  } = useDelete<Response>();
  const [otpModal, setOtpModal] = useState(false);
  const [mfaSessionId, setMfaSessionId] = useState('');

  const unlinkProcess = async (subClauseId: string, documentID: string) => {
    await deleteData(
      accessToken as string,
      `clauses/${subClauseId}/unlink-document/`,
      {
        process_id: process.id,
        document_ids: [documentID],
      },
    );
  };

  const assetColumns: any = [
    {
      headerName: 'Document Id',
      field: 'doc_id',
      sortable: true,
      resizable: true,
      filter: true,

      valueFormatter: (params: any) =>
        getValueOrDefault(params?.data, 'doc_id'),
    },
    {
      headerName: 'Title',
      field: 'title',
      resizable: true,
      filter: true,
      cellRenderer: (params: any) => (
        <div
          className="text-primary-500 cursor-pointer"
          onClick={() => router.push('/document/' + params.data.id)}
        >
          {params.data.title || '--'}
        </div>
      ),
      minWidth: 350,
      flex: 2,
    },
    {
      headerName: 'Assignees',
      field: 'assignees.name',
      sortable: false,
      resizable: true,
      filter: 'agMultiColumnFilter',
      filterValueGetter: (params: any) => {
        return params?.data?.assignees
          ? params?.data?.assignees
              ?.map((process: { name: string }) => {
                return `${process.name}`;
              })
              .join(', ')
          : '-';
      },
      getQuickFilterText: (params: any) => {
        return params?.data?.assignees
          ?.map((process: any) => {
            return `${process.name}`;
          })
          .join(', ');
      },
      valueFormatter: (params: any) => {
        return params?.data?.assignees
          ? params?.data?.assignees
              ?.map((process: any) => {
                return `${process.name}`;
              })
              .join(', ')
          : '-';
      },
    },
    {
      headerName: 'Status',
      field: 'status',
      resizable: true,
      filter: false,
    },
    {
      headerName: 'Compliant',
      field: 'complaint',
      sortable: true,
      resizable: true,
      filter: false,
      cellRenderer: (params: any) => (
        <div
          className={cn(
            'text-primary-500 cursor-pointer h-5 w-5 flex items-center justify-center  rounded-full absolute top-[50%] translate-y-[-50%]',
            params.data.is_compliant ? 'bg-green-200' : 'bg-red-200',
          )}
        >
          {params.data.is_compliant ? (
            <Check className="h-3 w-3" color="#fff" />
          ) : (
            <Minus className="h-3 w-3" color="#fff" />
          )}
        </div>
      ),
    },
    {
      headerName: 'Action',
      field: 'action',
      resizable: true,
      filter: false,
      cellRenderer: (params: any) =>
        canEdit ? (
          <>
            <Dialog open={otpModal} onOpenChange={setOtpModal}>
              {otpModal && (
                <OtpModal
                  setOtpModal={setOtpModal}
                  sessionId={mfaSessionId}
                  refetchDocumentData={reFetch}
                />
              )}
            </Dialog>

            <Dialog>
              <DialogTrigger>
                {hasAccess(AccessActions.CanLinkDocument, user) ? (
                  <div
                    className="text-primary-500 cursor-pointer h-10 w-10 flex items-center justify-center bg-white-200 rounded-full absolute top-[50%] translate-y-[-50%] hover:bg-white-300"
                    onClick={() => console.log(params, process)}
                  >
                    <UnlinkIcon className="h-6 w-6" />
                  </div>
                ) : (
                  '-'
                )}
              </DialogTrigger>
              <DeleteModal
                title={'Unlink document'}
                infoText={'Are you sure you want to unlink this document? '}
                btnText={'Unlink'}
                onClick={() => unlinkProcess(subClause.id, params.data.id)}
                btnLoading={deleteLoading}
              />
            </Dialog>
          </>
        ) : (
          '-'
        ),
    },
  ];

  useEffect(() => {
    if (response) {
      if (cfr_enabled && response.step === 'mfa') {
        setMfaSessionId(response.id);
        setOtpModal(true);
      } else {
        reFetch();
      }
    }

    if (error) {
      reFetch();
    }
  }, [response, error]);

  const isCompliant = process.documents.some(
    (document) => document.is_compliant,
  );

  return (
    <Accordion type="single" collapsible className="w-full mt-3">
      <AccordionItem value="item-1" className="">
        <AccordionTrigger
          className="group"
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <div className="flex flex-1 items-center justify-between mr-2">
            <div className="flex items-center gap-2">
              <span>{index + 1}</span>
              <div className="text-dark-300 text-base leading-7 font-medium">
                {process.name}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {canEdit && (
                <Dialog
                  open={editProcessModal}
                  onOpenChange={setEditProcessModal}
                >
                  <DialogTrigger
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedProcess(process.id);
                      setSelectedDocuments(process.documents.map((e) => e.id));
                    }}
                  >
                    {hasAccess(AccessActions.CanLinkDocument, user) && (
                      <button className="h-7 w-7 rounded-full bg-white-200 hidden items-center justify-center group-hover:flex cursor-pointer hover:bg-white-300">
                        <EditIcon height="20" width="20" />
                      </button>
                    )}
                  </DialogTrigger>
                  <LinkProcessModal
                    subclauseId={subClause.id}
                    edit
                    selectedProcessId={selectedProcess}
                    selectedDocumentId={selectedDocuments}
                    setShowModal={setEditProcessModal}
                    showModal={editProcessModal}
                    refetch={reFetch}
                    processes={processes}
                  />
                </Dialog>
              )}

              <div className="h-7 w-7 flex justify-center items-center">
                <div
                  className={cn(
                    'h-5 w-5 flex items-center justify-center  rounded-full',
                    isCompliant ? 'bg-green-200' : 'bg-red-200',
                  )}
                >
                  {isCompliant ? (
                    <CheckIcon height="16" width="16" />
                  ) : (
                    <Minus height="16" width="16" color="#fff" />
                  )}
                </div>
              </div>
            </div>
          </div>
        </AccordionTrigger>
        <AccordionContent className="py-4 px-5 flex flex-col gap-4">
          <div>
            <CommonTable
              data={{
                records: process.documents,
              }}
              columnDefs={assetColumns}
              searchBox={false}
            />
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export default ProcessDocument;
