import { Minus, Search } from 'lucide-react';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import CheckIcon from '@/assets/outline/check';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/common/accordion';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import { Dialog, DialogTrigger } from '@/components/common/dialog';
import Layout from '@/components/common/sidebar/layout';
import Tabs from '@/components/common/tabs';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { IProcess } from '@/interfaces/process';
import { IStandardRecord, IStandardView } from '@/interfaces/standard';
import { hasAccess } from '@/utils/roleAccessConfig';
import { cn } from '@/utils/styleUtils';

import ProcessDocument from '../processDocument';
import LinkProcessModal from './linkProcessModal';
import { filterClauses, transformClause } from '@/utils/transformClause';

const StandardHubInnerPage = () => {
  const [activeTab, setActiveTab] = useState<number>(0);
  const [activeTab2, setActiveTab2] = useState<number>(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredData, setFilteredData] = useState<IStandardRecord[] | []>([]);
  const [openLinkProcessModal, setOpenLinkProcessModal] = useState(false);
  const [transformedClauses, setTransformedClauses] = useState<any[]>([]);

  const { accessToken, user } = useAuthStore();
  const param = useParams();
  const standardId = param?.standardId;
  const [filter, setFilter] = useState({});

  const { data, isLoading, error, reFetch } = useFetch<IStandardView>(
    accessToken,
    `clauses/${standardId}`,
    filter,
  );
  const { data: processes } = useFetch<{ records: IProcess[] }>(
    accessToken,
    `processes`,
    {},
  );
  const breadcrumbData = [
    {
      name: 'Standard Hub',
      link: '/standard',
    },
    {
      name: data?.standard?.title || '',
      link: '#',
    },
  ];

  const tabsData = [
    {
      name: `Requirements (${data?.summary.total_document_count || '0'})`,
      textColor: 'text-dark-100',
      onClick: () => setFilter({}),
    },
    {
      name: `Compliant (${data?.summary.compliant_document_count || '0'})`,
      textColor: 'text-dark-100',
      onClick: () => setFilter({ is_compliant: true }),
    },
    {
      name: `Non Compliant (${
        data?.summary.non_compliant_document_count || '0'
      })`,
      textColor: 'text-dark-100',
      onClick: () => setFilter({ is_compliant: false }),
    },
  ];

  const tabsData2 = [{ name: 'Linked Processes', textColor: 'text-dark-100' }];

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!transformedClauses) return;

    const term = e.target.value.toLowerCase();
    setSearchTerm(term);

    const searchItems = filterClauses(transformedClauses, term);
    setFilteredData(searchItems || []);
  };

  useEffect(() => {
    reFetch();
  }, [filter]);

  useEffect(() => {
    if (!data?.records?.length) return;

    const transformItems = data.records.map((record) =>
      transformClause(record),
    );

    setTransformedClauses(transformItems);
  }, [data]);

  return (
    <Layout>
      <div className=" my-5">
        <div className="flex flex-col">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-[1.75rem] leading-10">
            {data?.standard?.title}
          </div>
        </div>
        <div className="mt-5">
          <Tabs
            tabsData={tabsData}
            setActiveTab={setActiveTab}
            activeTab={activeTab}
          />
          {data?.records.length === 0 ? (
            <div className="flex flex-col items-center justify-center my-20">
              <div>
                <Image
                  src={'/zeroStateTable.svg'}
                  alt=""
                  width={100}
                  height={100}
                />
              </div>
              <div className="text-base font-medium leading-6 text-grey-200 mt-2">
                {activeTab === 2
                  ? 'All clauses are compliant'
                  : activeTab === 1
                  ? 'No clauses are compliant'
                  : 'No data to show'}
              </div>
            </div>
          ) : (
            <>
              <div className="py-2.5 px-3 bg-white flex items-center rounded-lg gap-1 border border-grey-100 max-w-[40rem] mt-6 focus-within:border-primary-100">
                <Search className="h-5 w-5 " color="#B9B9B9" />
                <input
                  type="text"
                  placeholder="Search by clause or number"
                  className="flex-1 focus-visible:outline-none"
                  value={searchTerm}
                  onChange={handleSearch}
                />
              </div>
              <div className="mt-4">
                {(
                  (filteredData?.length > 0 && filteredData) ||
                  transformedClauses
                )?.map((clause, index) => (
                  <RecursiveAccordion
                    key={clause.id || index}
                    clause={clause}
                    processes={processes}
                    reFetch={reFetch}
                    tabsData2={tabsData2}
                    activeTab2={activeTab2}
                    setActiveTab2={setActiveTab2}
                    user={user}
                    openLinkProcessModal={openLinkProcessModal}
                    setOpenLinkProcessModal={setOpenLinkProcessModal}
                  />
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    </Layout>
  );
};

const RecursiveAccordion = ({
  clause,
  processes,
  reFetch,
  tabsData2,
  activeTab2,
  setActiveTab2,
  user,
  openLinkProcessModal,
  setOpenLinkProcessModal,
}: {
  clause: any;
  processes: any;
  reFetch: any;
  tabsData2: any;
  activeTab2: any;
  setActiveTab2: any;
  user: any;
  openLinkProcessModal: any;
  setOpenLinkProcessModal: any;
}) => {
  return (
    <Accordion type="single" collapsible className="w-full mt-2">
      <AccordionItem value={`item-${clause.id}`}>
        <AccordionTrigger className="">
          <div className="flex flex-1 items-center justify-between mr-2">
            <div className="flex items-center gap-2">
              <span>{clause.clause_no}</span>
              <div className="text-dark-300 text-base leading-6 font-medium">
                {clause.title}
              </div>
            </div>

            <div className="flex items-center gap-5">
              <div className="h-7 w-7 flex justify-center items-center">
                <div
                  className={cn(
                    'h-5 w-5 flex items-center justify-center rounded-full',
                    clause.is_compliant ? 'bg-green-200' : 'bg-red-200',
                  )}
                >
                  {clause.is_compliant ? (
                    <CheckIcon height="16" width="16" />
                  ) : (
                    <Minus height="16" width="16" color="#fff" />
                  )}
                </div>
              </div>
            </div>
          </div>
        </AccordionTrigger>

        <AccordionContent className="py-2 px-5 flex flex-col">
          {/* Render child clauses or sub-clauses */}
          {clause.children?.map((child: any, index: number) => {
            // Recursively render child elements (could be sub-clause or nested clauses)
            return (
              <RecursiveAccordion
                key={child.id || index}
                clause={child}
                processes={processes}
                reFetch={reFetch}
                tabsData2={tabsData2}
                activeTab2={activeTab2}
                setActiveTab2={setActiveTab2}
                user={user}
                openLinkProcessModal={openLinkProcessModal}
                setOpenLinkProcessModal={setOpenLinkProcessModal}
              />
            );
          })}

          {clause.description ? (
            <div className="mt-4">
              <div className="text-dark-300 text-lg font-semibold leading-7 mb-2">
                Clause Description:
              </div>
              <div className="text-dark-100 text-base leading-6 font-medium">
                <TextWithLineBreaks text={clause.description} />
              </div>
            </div>
          ) : (
            ''
          )}

          {/* clause question */}
          {clause.question ? (
            <div className="mt-4">
              <div className="text-dark-300 text-lg font-semibold leading-7 mb-2">
                Clause Question:
              </div>
              <div className="text-dark-100 text-base leading-6 font-medium">
                <TextWithLineBreaks text={clause.question} />
              </div>
            </div>
          ) : (
            ''
          )}

          {clause?.question && (
            <div className="mt-4">
              <div className="flex items-center justify-between">
                <Tabs
                  tabsData={tabsData2}
                  activeTab={activeTab2}
                  setActiveTab={setActiveTab2}
                />
                {hasAccess(AccessActions.CanLinkDocument, user) && (
                  <Dialog
                    open={openLinkProcessModal}
                    onOpenChange={setOpenLinkProcessModal}
                  >
                    <DialogTrigger>
                      <PrimaryButton text="Link Process" size="medium" />
                    </DialogTrigger>
                    <LinkProcessModal
                      subclauseId={clause.id}
                      setShowModal={setOpenLinkProcessModal}
                      showModal={openLinkProcessModal}
                      refetch={reFetch}
                      alreadyLinkedProcessId={clause.processes.flatMap(
                        (process: any) => process.id,
                      )}
                      processes={processes}
                    />
                  </Dialog>
                )}
              </div>
              <div>
                {clause?.processes?.length > 0 &&
                  clause?.processes?.map((process: any, y: number) => (
                    <ProcessDocument
                      key={y}
                      process={process}
                      subClause={clause}
                      reFetch={reFetch}
                      processes={processes}
                      index={y}
                    />
                  ))}
              </div>
            </div>
          )}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

const TextWithLineBreaks = ({ text }: { text: string }) => {
  return (
    <div>
      {text.split('\n').map((line, index) => (
        <React.Fragment key={index}>
          {line}
          <br />
        </React.Fragment>
      ))}
    </div>
  );
};

export default StandardHubInnerPage;
