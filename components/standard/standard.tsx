import React from 'react';

import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { IStandardData } from '@/interfaces/standard';

import Breadcrumb from '../common/breadcrumb';
import Loader from '../common/loader';
import Layout from '../common/sidebar/layout';
import Card from './card';

const Standard = () => {
  const { accessToken } = useAuthStore();
  const { data, isLoading, error, reFetch } = useFetch<{
    records: IStandardData[];
  }>(accessToken as string, 'standards', {});

  if (error) {
    return (
      <div className="rounded-md border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
        {/* @ts-expect-error Server Component */}
        <p className="p-4">{error.response?.data?.error}</p>
      </div>
    );
  }
  const breadcrumbData = [
    {
      name: 'Standard Hub',
      link: '#',
    },
  ];
  return (
    <div>
      <Layout>
        <div className=" my-5">
          <div className="flex flex-col">
            <Breadcrumb data={breadcrumbData} />
            <div className="text-dark-300 font-semibold text-3xl leading-10">
              Standard Hub
            </div>
          </div>
          {isLoading ? (
            <Loader className="h-[50vh]" />
          ) : (
            <div className="grid grid-cols-3 gap-5 mt-6">
              {data?.records?.map((e, i) => (
                <Card key={i} data={e} />
              ))}
            </div>
          )}
        </div>
      </Layout>
    </div>
  );
};

export default Standard;
