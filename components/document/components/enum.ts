import moment from 'moment';

export const DocumentFilterConstants = {
  ALL: {},
  PUBLISHED: { status: 'Published' },
  ASSIGNED_TO_ME: { is_assigned_to_me: true },
  NEEDS_APPROVAL: { is_waiting_for_approval: true },
  IS_APPROVED_APPROVAL: { is_approved_approval: true },
  IS_REJECTED_APPROVAL: { is_rejected_approval: true },
  REVIEW_IN_30_DAYS: {
    next_review_date_range: `${moment().format('YYYY-MM-DD')}:${moment()
      .add(30, 'days')
      .format('YYYY-MM-DD')}`,
  },
  PAST_DUE: {
    next_review_date_range: `:${moment().format('YYYY-MM-DD')}`,
  },
};
