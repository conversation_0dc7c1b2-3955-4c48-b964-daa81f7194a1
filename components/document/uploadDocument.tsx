import React, { useCallback, useEffect, useState } from 'react';
import SideBarWrapper from '../common/sidebar/layout';
import BackButton from '@/assets/backButton.svg';
import Image from 'next/image';
import { Tooltip, TooltipContent, TooltipTrigger } from '../common/tooltip';
import { useDropzone } from 'react-dropzone';
import FileCard from '../common/modals/uploadModal/fileCard';
import TertiaryButton from '../common/button/tertiaryButton';
import InfoCircle from '@/assets/outline/infoCircle';
import { Label } from '../common/label';
import { Input } from '../common/input';
import ToggleSwitch from '../common/toogleSwitch';
import ApprovalTypeSelector, { ApprovalType } from './approvalTypeSelector';
import Calendar from '@/components/common/calendar';
import moment from 'moment';
import { Search } from 'lucide-react';
import AddApprovers from './addApprovers';
import PrimaryButton from '../common/button/primaryButton';
import EditIcon from '@/assets/outline/edit';
import { Select } from '@radix-ui/react-select';
import {
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../common/select';
import { useAuthStore } from '@/globalProvider/authStore';
import axios from 'axios';
import { useParams } from 'next/navigation';
import { usePost } from '@/hooks/usePost';
import CheckIcon from '@/assets/outline/check';
import { cn } from '@/utils/styleUtils';
import Link from 'next/link';
import useFetch from '@/hooks/useFetch';
import { IDocumentDetails } from '@/interfaces/document';
import { toast } from 'react-toastify';
import { useRouter } from 'next/router';
import { z } from 'zod';
import useValidators from '@/hooks/useValidator';

const acceptFileTypes = {
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
    '.docx',
    '.doc',
  ],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
    '.xlsx',
  ],
  'application/vnd.ms-excel': ['.xls', '.csv'],
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png'],
};

const isMulti = false;

const periodMap: Record<
  'Monthly' | 'Quarterly' | 'Half Yearly' | 'Annually',
  number
> = {
  Monthly: 1,
  Quarterly: 3,
  'Half Yearly': 6,
  Annually: 12,
};

type ReviewPeriod = keyof typeof periodMap;

export const uploadDocumentSchema = {
  version: z
    .string()
    .nonempty({ message: 'Version is required' })
    .refine(
      (val) => {
        const num = parseFloat(val);
        return !isNaN(num) && num !== 0;
      },
      {
        message: 'Version cannot be 0 or 0.0',
      },
    ),
};

const UploadDocumentComponent = () => {
  const [approvalType, setApprovalType] = useState<ApprovalType>('Sequential');
  const [loading, setLoading] = useState(false);
  const [addedFile, setAddedFile] = useState<File[] | null>(null);
  const [publishDate, setPublishDate] = useState(moment().format('YYYY-MM-DD'));
  const [nextReviewDate, setNextReviewDate] = useState<string>('');
  const [requireApproval, setRequireApproval] = useState<boolean>(true);
  const [version, setVersion] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [approversData, setApproversData] = useState([]);
  const [reviewPeriod, setReviewPeriod] = useState('');
  const [customReviewDate, setCustomReviewDate] = useState(false);
  const [fileError, setFileError] = useState(false);
  const [reviewPeriodError, setReviewPeriodError] = useState(false);
  const [approverError, setApproverError] = useState(false);
  const [existingApprovers, setExistingApprovers] = useState([]);
  const { accessToken } = useAuthStore();
  const param = useParams();
  const { postData, isLoading, response, error } = usePost();
  const router = useRouter();

  const [formState, setFormState] = useState({
    version: '',
  });

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setAddedFile(acceptedFiles);
  }, []);
  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    multiple: isMulti,
    accept: acceptFileTypes,
  });

  const {
    data,
    isLoading: documentLoading,
    reFetch: refetchDocumentData,
  } = useFetch<{
    record: IDocumentDetails;
  }>(accessToken, `documents/${param?.documentId}`, {});

  const approvalOptions = [
    {
      value: 'Sequential',
      title: 'Sequential Approval',
      description:
        'Approvers will be notified one after another in the order you specify',
    },
    {
      value: 'Parallel',
      title: 'Parallel Approval',
      description: 'All approvers will be notified simultaneously',
    },
  ];

  const handleSearch = (e: any) => {
    const term = e.target.value.toLowerCase();
    setSearchTerm(term);
  };

  const { validationErrors, startValidation, reset } = useValidators({
    schemas: uploadDocumentSchema,
    values: formState,
  });

  const submitWithApprovers = async () => {
    const { hasValidationErrors } = await startValidation();

    setFileError(false);
    setApproverError(false);

    if (!addedFile || addedFile?.length === 0) {
      setFileError(true);
      return;
    }

    if (approversData?.length === 0) {
      setApproverError(true);
      return;
    }

    if (!hasValidationErrors) {
      setLoading(true);
      const formData = new FormData();
      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;
      const url = `${baseUrl}/${productVersion}/file/upload?document_for=document_hub&sub_path=v${formState.version}`;
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${accessToken}`,
        },
        onUploadProgress: () => {
          setLoading(true);
        },
      };
      formData.append('file', addedFile?.[0] as unknown as Blob);
      axios.post(url, formData, config).then((response) => {
        const file_data = {
          file_path: response.data.file_path,
          file_extension: response.data.file_ext,
          version: formState.version,
        };
        const body = {
          ...file_data,
          approval_request: {
            flow: approvalType,
            approvers: approversData.map((user: any, index: number) => ({
              sequence_number: index + 1,
              user_id: user?.user_id,
            })),
          },
        };
        async function fetch() {
          await postData(
            accessToken as string,
            `documents/${param.documentId}/send-for-approval`,
            body,
          );
        }
        fetch();
      });
    }
  };

  useEffect(() => {
    setFileError(false);
    setApproverError(false);
    setReviewPeriodError(false);
  }, [approversData, addedFile, reviewPeriod]);

  const submitWithOutApprovers = async () => {
    const { hasValidationErrors } = await startValidation();

    setFileError(false);
    setReviewPeriodError(false);

    if (!reviewPeriod || reviewPeriod === '') {
      setReviewPeriodError(true);
      return;
    }

    if (!addedFile || addedFile?.length === 0) {
      setFileError(true);
      return;
    }

    if (!hasValidationErrors) {
      setLoading(true);
      const formData = new FormData();
      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;
      const url = `${baseUrl}/${productVersion}/file/upload?document_for=document_hub&sub_path=v${formState.version}`;
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${accessToken}`,
        },
        onUploadProgress: () => {
          setLoading(true);
        },
      };
      formData.append('file', addedFile?.[0] as unknown as Blob);
      axios.post(url, formData, config).then((response) => {
        const file_data = {
          file_path: response.data.file_path,
          file_extension: response.data.file_ext,
          version: formState.version,
        };
        const body = {
          ...file_data,
          review_period: reviewPeriod,
          ...(data?.record && !data.record.document_version
            ? { next_review_date: nextReviewDate }
            : {}),
        };
        async function fetch() {
          await postData(
            accessToken as string,
            `documents/${param.documentId}/publish`,
            body,
          );
        }
        fetch();
      });
    }
  };

  useEffect(() => {
    if (response) {
      toast.success('Document uploaded successfully');
      router.push(`/document/${param.documentId}`);
    }
    if (error) {
      toast.error('Error uploading document');
      setLoading(false);
    }
  }, [response, error]);

  function transformApprovers(approvers: any) {
    return approvers?.map((approver: any, index: number) => ({
      sequence_number: index + 1,
      user: { ...approver },
    }));
  }

  useEffect(() => {
    if (
      existingApprovers.length === 0 &&
      (data?.record?.approvers?.length ?? 0) > 0
    ) {
      const exisitng = transformApprovers(data?.record?.approvers);
      setExistingApprovers(exisitng);
    }
  }, [data]);

  return (
    <SideBarWrapper>
      <div className="flex flex-col flex-1">
        <div className="my-5">
          <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-5">
            <Tooltip>
              <TooltipTrigger>
                <Link
                  className="w-10 h-10 flex items-center justify-center  bg-white-200 rounded-full hover:bg-white-300 cursor-pointer"
                  href={`/document/${param?.documentId}`}
                >
                  <Image src={BackButton} alt="" />
                </Link>
              </TooltipTrigger>
              <TooltipContent>
                <div className="text-sm text-dark-300">Back</div>
              </TooltipContent>
            </Tooltip>
            Upload Document
          </div>
        </div>
        <div className="mb-10">
          <div className="flex items-center gap-2 text-sm font-medium leading-5 text-dark-300 p-3 rounded-lg bg-white-150 mb-2">
            <div className="h-9 w-9 bg-[#91909A29] flex items-center justify-center rounded-full">
              <InfoCircle />
            </div>
            Editing and saving a document will overwrite your existing draft.
          </div>
          <div className="text-base leading-6 font-medium text-dark-100 mb-2.5">
            Attach file<span className="text-red-200">*</span>
          </div>
          <div>
            <div className="mb-5">
              <div
                className={
                  fileError
                    ? 'mb-3 min-h-32 bg-white-100  border rounded-xl flex items-center justify-center flex-col gap-2 hover:bg-[#F8F8F8] p-2 border-red-200'
                    : 'mb-3 min-h-32 bg-white-100  border border-dashed border-[#C7C7CC] rounded-xl flex items-center justify-center flex-col gap-2 hover:bg-[#F8F8F8] p-2'
                }
                {...getRootProps()}
              >
                {!(addedFile?.length && addedFile?.length > 0) && (
                  <div className="text-sm font-medium leading-5 text-[#49474E]">
                    Upload or Drag and drop to upload your file
                  </div>
                )}

                <input {...getInputProps()} />
                <div className="flex justify-center items-center flex-wrap gap-2">
                  {addedFile?.map((file, index) => (
                    <FileCard
                      key={index}
                      file={file}
                      setAddedFile={setAddedFile}
                    />
                  ))}
                </div>
                <TertiaryButton
                  text={
                    isMulti
                      ? 'Add files'
                      : addedFile?.length && addedFile?.length > 0
                      ? 'Replace'
                      : 'Upload file'
                  }
                  size="small"
                />
              </div>
              {fileError ? (
                <div className="text-xs font-semibold leading-5 text-left text-red-200">
                  Attachment is required
                </div>
              ) : (
                <></>
              )}
            </div>
            <div className="flex flex-col gap-2.5 flex-1 mb-5">
              <Label
                htmlFor="version"
                className="text-base flex justify-between items-center font-medium leading-6 text-dark-100"
              >
                <Label
                  htmlFor="version"
                  className="text-base font-medium leading-6 text-dark-100"
                >
                  Version<span className="text-red-200">*</span>
                </Label>
                <p className="text-grey-300">
                  Currently published version:{' '}
                  {data?.record?.document_version?.version_number || 'NA'}
                </p>
              </Label>
              <Input
                placeholder="Version"
                id="version"
                type="number"
                value={formState.version}
                onChange={(e) => {
                  setFormState((pre) => ({ ...pre, version: e.target.value }));
                }}
                errorMsg={validationErrors?.version[0]}
              />
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-16 justify-between mb-5 bg-gray-50 px-6 py-5 rounded-xl">
                <div>
                  <div className="flex items-center gap-2 flex-nowrap">
                    <h3 className="text-base font-medium text-approval-text-primary">
                      Requires document approval
                    </h3>

                    <Tooltip>
                      <TooltipTrigger>
                        <div
                          className="w-10 h-10 flex items-center justify-centerrounded-full cursor-pointer"
                          onClick={() => console.log('first')}
                        >
                          <InfoCircle />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="text-sm text-dark-300">
                          Optionally, set a specific date for the next review.
                          If selected, the recurring review cycle will be
                          counted from this date instead of the document’s
                          publish date.
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <p className="text-grey-300">
                    Enable if this document requires approval workflow
                  </p>
                </div>
                <div>
                  <ToggleSwitch
                    initialState={requireApproval}
                    onChange={(state) => {
                      setRequireApproval(state);
                      setPublishDate('');
                      setReviewPeriod('');
                      setCustomReviewDate(false);
                      if (!state) {
                        setPublishDate(moment().format('YYYY-MM-DD'));
                        setReviewPeriod('');
                      }
                    }}
                  />
                </div>
              </div>
            </div>
            {requireApproval && (
              <>
                <div className="mb-5">
                  <div className="w-full">
                    <ApprovalTypeSelector
                      value={approvalType}
                      onChange={setApprovalType}
                    />
                  </div>
                </div>

                <AddApprovers
                  approvalType={approvalType}
                  approversData={(data: any) => setApproversData(data)}
                  existingApprovers={existingApprovers}
                  assignees={data?.record?.assignees}
                />
                {approverError ? (
                  <div className="text-xs font-semibold leading-5 text-left text-red-200">
                    Atleast one approver is required
                  </div>
                ) : (
                  <></>
                )}
              </>
            )}

            {!requireApproval && (
              <div className="p-2 bg-white-100 border border-white-300 rounded-lg">
                <div className="flex items-center p-2 pb-4 border-b border-white-300">
                  Set Review Schedule{' '}
                  <Tooltip>
                    <TooltipTrigger>
                      <div
                        className="w-10 h-10 flex items-center justify-center rounded-full cursor-pointer"
                        onClick={() => console.log('first')}
                      >
                        <InfoCircle />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="text-sm text-dark-300">
                        Set the recurring time period after which the document
                        should be reviewed.
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <div className="py-4 px-3">
                  <div className="flex gap-4 items-center text-base text-dark-300 leading-6 font-medium">
                    <p className="text-grey-300 font-medium text-base leading-6 whitespace-nowrap">
                      Review Period<span className="text-red-200">*</span>
                    </p>
                    <div>
                      <Select
                        value={reviewPeriod}
                        onValueChange={(value: ReviewPeriod) => {
                          setReviewPeriod(value);
                          setNextReviewDate(
                            moment(publishDate)
                              .add(periodMap[value], 'months')
                              .format('YYYY-MM-DD'),
                          );
                        }}
                      >
                        <SelectTrigger className={'w-[240px]'} id="review">
                          <SelectValue placeholder="---" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Monthly">Monthly</SelectItem>
                          <SelectItem value="Quarterly">Quarterly</SelectItem>
                          <SelectItem value="Half Yearly">
                            Half Yearly
                          </SelectItem>
                          <SelectItem value="Annually">Annually</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  {reviewPeriodError ? (
                    <div className="text-xs mt-1 font-semibold leading-5 text-left text-red-200">
                      Review period required
                    </div>
                  ) : (
                    <></>
                  )}
                </div>

                {!data?.record?.document_version && (
                  <div className="flex items-center gap-4 justify-between pt-4 pb-2 px-3 border-t border-white-300">
                    <div className="flex gap-4 text-base text-dark-300 leading-6 font-medium">
                      <div className="flex items-center">
                        <Tooltip>
                          <TooltipTrigger>
                            <div
                              className="w-10 h-10 flex items-center justify-center rounded-fullcursor-pointer"
                              onClick={() => console.log('first')}
                            >
                              <InfoCircle />
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-sm text-dark-300">
                              Optionally, set a specific date for the next
                              review. If selected, the recurring review cycle
                              will be counted from this date instead of the
                              document’s publish date.
                            </div>
                          </TooltipContent>
                        </Tooltip>
                        <h3 className="text-base mt-0.5 font-medium text-approval-text-primary">
                          Pick a specific date for the next review
                        </h3>
                      </div>

                      <ToggleSwitch
                        initialState={customReviewDate}
                        onChange={(state) => {
                          setCustomReviewDate(state);
                          if (!state) {
                            setPublishDate(moment().format('YYYY-MM-DD'));
                          }
                        }}
                      />
                    </div>
                    <div className="flex gap-4 items-center text-base text-dark-300 leading-6 font-medium">
                      <p className="text-grey-300 font-medium text-base leading-6 whitespace-nowrap">
                        Next Review Date
                      </p>
                      <Calendar
                        className={`${
                          !customReviewDate ? 'bg-white-150' : 'bg-slate-50'
                        } w-full`}
                        selectedDate={
                          (customReviewDate && nextReviewDate) || ''
                        }
                        disabled={!customReviewDate}
                        onDateChange={(date) => {
                          if (date) {
                            setNextReviewDate(
                              moment(date as string).format('YYYY-MM-DD'),
                            );
                          } else {
                            setNextReviewDate('');
                          }
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
          <div className="flex justify-end gap-4 mt-5">
            {reviewPeriod && (
              <div className="flex-1 bg-green-50 rounded-md">
                <div className="flex gap-2 px-4 py-2 items-center text-base text-dark-300 leading-6 font-medium">
                  <div
                    className={cn(
                      'h-5 w-5 flex items-center justify-center bg-green-200 rounded-full',
                    )}
                  >
                    <CheckIcon height="16" width="16" />
                  </div>

                  <p className="text-base font-medium">
                    Next review scheduled for:{' '}
                    {reviewPeriod ? (
                      <>
                        {nextReviewDate
                          ? moment(nextReviewDate).format('MMM D YYYY')
                          : moment(publishDate)
                              .add(
                                periodMap[reviewPeriod as ReviewPeriod] || 0,
                                'months',
                              )
                              .format('MMM D YYYY')}
                        {} (repeats {reviewPeriod} from{' '}
                        {moment(publishDate).format('MMM D YYYY')})
                      </>
                    ) : (
                      '---'
                    )}
                  </p>
                </div>
              </div>
            )}

            <PrimaryButton
              text={requireApproval ? 'Send for Approval' : 'Publish'}
              size="medium"
              onClick={() =>
                requireApproval
                  ? submitWithApprovers()
                  : submitWithOutApprovers()
              }
              isLoading={loading || isLoading}
            />
          </div>
        </div>
      </div>
    </SideBarWrapper>
  );
};

export default UploadDocumentComponent;
