import axios, { AxiosError } from 'axios';
import { Download, FileText, TriangleAlertIcon, Upload } from 'lucide-react';
import moment from 'moment';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import ZeroStateLog from '@/assets/document/zeroStateLog.svg';
import DeleteIcon from '@/assets/outline/delete';
import DocumentIcon from '@/assets/outline/document';
import EditIcon from '@/assets/outline/edit';
import InfoCircle from '@/assets/outline/infoCircle';
import UploadIcon from '@/assets/outline/upload';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/common/dialog';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { IComment } from '@/interfaces/comment';
import {
  IDocumentDetails,
  IDocumentVersion,
  ILogs,
} from '@/interfaces/document';
import { getFileNameFromPath } from '@/utils/helper';
import { hasAccess } from '@/utils/roleAccessConfig';
import { cn } from '@/utils/styleUtils';
import { formatDate } from '@/utils/time';
import { truncateWithEllipsis } from '@/utils/truncateText';

import Breadcrumb from '../common/breadcrumb';
import DeleteButton from '../common/button/deleteButton';
import LinkButton from '../common/button/linkButton';
import PrimaryButton from '../common/button/primaryButton';
import SecondaryButton from '../common/button/secondaryButton';
import TertiaryButton from '../common/button/tertiaryButton';
import { DetailsText } from '../common/infoDetail';
// import AddResponseModal from "./components/modals/addResponseModal";
// import CreateDocumentModal from "./components/modals/createDocumentModal";
import Loader from '../common/loader';
import ConfirmModal from '../common/modals/confirmModal';
import DeleteModal from '../common/modals/deleteModal';
import DocumentViewModal from '../common/modals/documentViewModal';
import SideBarWrapper from '../common/sidebar/layout';
import Status from '../common/status';
import Tabs from '../common/tabs';
import { Tooltip, TooltipContent, TooltipTrigger } from '../common/tooltip';
import UploadModal from '../common/uploadModal';
import LogsCard from './components/logsCard';
import AddResponseModal from './components/modals/addResponseModal';
import CreateDocumentModal from './components/modals/createDocumentModal';
import JustificationModal from './components/modals/justificationModal';
import OtpModal from './components/modals/otpModal';
import VersionCard from './components/versionCard';
import { htmlToPdf } from '@/utils/htmlToPdf';
import CheckIcon from '@/assets/outline/check';
import ToggleSwitch from '../common/toogleSwitch';
import Calendar from '../common/calendar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../common/select';
import EditApproversModal from './components/modals/editApprovers';
import Link from 'next/link';
import PlusIcon from '@/assets/outline/plus';
import WarningIcon from '@/assets/outline/warning';
import TickIcon from '@/assets/outline/tick';
import { getDaysUntilReview } from '@/utils/getDaysUntilReview';
import ReviewDateBanner from './components/banners/reviewDateBanner';
import ReviewDocumentModal from './components/modals/reviewDocumentModal';

interface ErrorResponse {
  error: string;
}

interface ApprovalResponse {
  id: string;
  step: string;
}

const periodMap: Record<
  'Monthly' | 'Quarterly' | 'Half Yearly' | 'Annually',
  number
> = {
  Monthly: 1,
  Quarterly: 3,
  'Half Yearly': 6,
  Annually: 12,
};

type ReviewPeriod = keyof typeof periodMap;

const DocumentView = () => {
  const [activeTab, setActiveTab] = useState<number>(0);
  const [openEditModal, setOpenEditModal] = useState<boolean>(false);
  const [openResponse, setOpenResponse] = useState<boolean>(false);
  const { accessToken, user } = useAuthStore();
  const cfr_enabled = useAuthStore(
    (state) => state.user?.company.is_cfr11_required,
  );
  const [confirmModal, setConfirmModal] = useState(false);
  const [openJustificationModal, setOpenJustifcationModal] = useState(false);
  const [openUploadModal, setOpenUploadModal] = useState(false);
  const [deleteVersion, setDeleteVersion] = useState(false);
  const [editVersion, setEditVersion] = useState(false);
  const [editPublishedVersion, setEditPublishedVersion] = useState(false);
  const [otpModal, setOtpModal] = useState(false);
  const [mfaSessionId, setMfaSessionId] = useState('');
  const [openDocumentModal, setOpenDocumentModal] = useState(false);
  const [openApprovalDocumentModal, setOpenApprovalDocumentModal] =
    useState(false);
  const [editApproversModal, setEditApproversModal] = useState(false);

  const [customReviewDate, setCustomReviewDate] = useState(false);
  const [publishDate, setPublishDate] = useState(moment().format('YYYY-MM-DD'));
  const [reviewPeriod, setReviewPeriod] = useState('');
  const [createNewVersion, setCreateNewVersion] = useState(false);
  const [reviewPeriodModal, setReviewPeriodModal] = useState(false);
  const [nextReviewDate, setNextReviewDate] = useState<string>('');

  const param = useParams();

  const router = useRouter();
  const { postData, response, isLoading, error } = usePost();
  const {
    postData: postApproval,
    response: approvalResponse,
    isLoading: approvalLoading,
    error: approvalError,
  } = usePost<ApprovalResponse>();

  const {
    data,
    isLoading: documentLoading,
    reFetch: refetchDocumentData,
  } = useFetch<{
    record: IDocumentDetails;
  }>(accessToken, `documents/${param?.documentId}`, {});
  const {
    data: documentComments,
    isLoading: commentsLoading,
    reFetch: refetchComments,
  } = useFetch<{ records: IComment[] }>(
    accessToken,
    `documents/${param?.documentId}/comments`,
    {},
  );

  // dynamic endpoints based on active tab
  const fetchLogs =
    activeTab === 2 ? `documents/${param?.documentId}/document-log` : undefined;
  const fetchVersions =
    activeTab === 1 ? `document-versions/${param?.documentId}` : undefined;

  const { data: logs, isLoading: logsLoading } = useFetch<{ records: ILogs[] }>(
    accessToken,
    fetchLogs,
    {},
  );

  const { data: versions, isLoading: versionLoading } = useFetch<{
    records: IDocumentVersion[];
  }>(accessToken, fetchVersions, {});

  const {
    deleteData,
    isLoading: deleteLoading,
    response: deleteResponse,
    error: deleteError,
  } = useDelete();

  const {
    deleteData: deleteApproval,
    isLoading: deleteApprovalLoading,
    response: deleteApprovalResponse,
    error: deleteApprovalError,
  } = useDelete();

  const breadcrumbData = [
    {
      name: 'Document Hub',
      link: `/document`,
      query: router.query.filter
        ? {
            filter: router.query.filter,
            page: router.query.page,
            pageSize: router.query.pageSize,
          }
        : undefined,
    },
    {
      name: 'Document View',
      link: '#',
    },
  ];

  const tabsData = hasAccess(AccessActions.CanEditDocument, user)
    ? [
        { name: 'Details', textColor: 'text-dark-100' },
        { name: 'Versions', textColor: 'text-dark-100' },
        { name: 'Logs', textColor: 'text-dark-100' },
      ]
    : [{ name: 'Details', textColor: 'text-dark-100' }];

  const handleApprove = () => {
    const body = {
      action: 'Approved',
    };

    async function fetch() {
      await postApproval(
        accessToken as string,
        `approvals/${data?.record?.approval.id}/action`,
        body,
      );
    }
    fetch();
  };

  useEffect(() => {
    if (response || error) {
      setConfirmModal(false);
      refetchDocumentData();
    }
  }, [response, error]);

  useEffect(() => {
    if (approvalResponse) {
      if (cfr_enabled && approvalResponse.step === 'mfa') {
        setConfirmModal(false);
        setMfaSessionId(approvalResponse.id);
        setOtpModal(true);
      } else {
        setConfirmModal(false);
        refetchDocumentData();
      }
    }
  }, [approvalResponse, approvalError]);

  const handleRejectMfa = (data: ApprovalResponse) => {
    if (data && cfr_enabled) {
      setConfirmModal(false);
      setMfaSessionId(data.id);
      setOtpModal(true);
    } else {
      refetchDocumentData();
    }
  };

  useEffect(() => {
    if (deleteError) {
      toast.error(
        ((deleteError as AxiosError).response?.data as ErrorResponse)?.error ||
          'An error occurred',
      );
    }
    if (deleteResponse) {
      setDeleteVersion(false);
      toast.success('Document deleted successfully');
      router.push('/document');
    }
  }, [deleteResponse, deleteError]);

  useEffect(() => {
    if (deleteApprovalError) {
      toast.error(
        ((deleteError as AxiosError)?.response?.data as ErrorResponse)?.error ||
          'An error occurred',
      );
    }
    if (deleteApprovalResponse) {
      setDeleteVersion(false);
      toast.success('Approval deleted successfully');
      refetchDocumentData();
    }
  }, [deleteApprovalResponse, deleteApprovalError]);

  const handleDownloadDocument = (
    path: string,
    documentTitle: string,
    versionNumber: number,
    extension?: string,
  ) => {
    console.log('extension', path, documentTitle, versionNumber, extension);
    const baseUrl = process.env.NEXT_PUBLIC_URL;
    const productVersion = process.env.NEXT_PUBLIC_VERSION;
    const filePath = encodeURIComponent(path);
    axios
      .get(
        `${baseUrl}/${productVersion}/file/presigned-url?file_path=${filePath}&expiration=600`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        },
      )
      .then((res) => {
        fetch(res.data.url)
          .then((res) => res.blob())
          .then((res) => {
            if (extension === 'html') {
              res.text().then((textContent) => {
                htmlToPdf({
                  htmlContent: textContent,
                  filename: `${documentTitle}_v${versionNumber}.pdf`,
                });
              });
            } else {
              const link = document.createElement('a');
              link.href = window.URL.createObjectURL(res);
              link.download = getFileNameFromPath(
                path,
                documentTitle,
                versionNumber,
              );
              link.click();
            }
          });
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const loadingData = documentLoading || commentsLoading;

  const haveApproverAssignee =
    data?.record?.approvers?.length &&
    data?.record?.approvers?.length > 0 &&
    data?.record?.assignees.length &&
    data?.record?.assignees.length > 0;

  const showReviewPeriod = (value: number) => {
    if (value === 0) {
      return '-';
    } else if (value === 1) {
      return 'Monthly';
    } else if (value === 3) {
      return 'Quarterly';
    } else if (value === 6) {
      return 'Half Yearly';
    } else if (value === 12) {
      return 'Annually';
    }
    return '-';
  };

  const handlePublish = () => {
    const body = {
      approval_id: data?.record?.approval?.id,
      review_period: reviewPeriod,
      ...(data?.record && !data.record.document_version
        ? { next_review_date: nextReviewDate }
        : {}),
    };
    async function fetch() {
      await postData(
        accessToken as string,
        `documents/${param.documentId}/publish-after-approval`,
        body,
      );
    }
    fetch();
  };

  return (
    <SideBarWrapper>
      <div className="flex flex-col flex-1">
        <div className=" my-5">
          <div>
            <Breadcrumb data={breadcrumbData} />
            {!loadingData ? (
              <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-2.5">
                {data?.record?.title}{' '}
                <Status type={data?.record?.status.toLowerCase() as string} />
              </div>
            ) : (
              ''
            )}
          </div>
          {!loadingData ? (
            <div className="mt-4">
              <Tabs
                tabsData={tabsData}
                activeTab={activeTab}
                setActiveTab={setActiveTab}
                tabRightSideElement={
                  (hasAccess(AccessActions.CanUploadDocument, user) ||
                    (user?.id &&
                      data?.record?.assignees?.some(
                        (e) => e.id === user?.id,
                      ))) &&
                  data?.record?.approval?.status !== 'Pending' &&
                  data?.record?.approval?.status !== 'Approved' ? (
                    <>
                      <Dialog
                        open={createNewVersion}
                        onOpenChange={setCreateNewVersion}
                      >
                        <DialogTrigger asChild>
                          <PrimaryButton
                            text={`${
                              data?.record?.document_version?.version_number
                                ? 'New Version'
                                : 'Create Document'
                            }`}
                            icon={
                              <PlusIcon className="w-4 h-4" color="#FFFFFF" />
                            }
                            iconPosition="left"
                            size="medium"
                          />
                        </DialogTrigger>
                        <DialogContent className="min-w-[38.625rem]">
                          <DialogHeader>
                            <DialogTitle>Create a new version</DialogTitle>
                          </DialogHeader>
                          <div className="mt-2">
                            <div className="flex gap-4">
                              <Link
                                href={
                                  data?.record?.assignees?.length !== 0
                                    ? `${param?.documentId}/upload-document`
                                    : '#'
                                }
                                className="w-64 h-36 flex-1"
                              >
                                <div className="w-full h-full flex flex-col items-center justify-center rounded-2xl bg-gray-50 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                                  <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-4">
                                    <UploadIcon className="w-6 h-6 text-gray-300" />
                                  </div>
                                  <h3 className="text-base font-medium text-dark-300 mb-1">
                                    Upload document
                                  </h3>
                                  <p className="text-xs font-semibold text-gray-400 mt-1 text-center">
                                    PDF, DOCX, XLSX, PPTX (Max 10MB)
                                  </p>
                                </div>
                              </Link>

                              <Link
                                href={
                                  data?.record?.assignees?.length !== 0
                                    ? (data?.record?.document_version
                                        ?.version_number ?? 0) > 0 &&
                                      data?.record?.document_version
                                        ?.file_extension === 'html'
                                      ? `/document/${param.documentId}/create-document?file_path=${data?.record?.document_version?.file_path}`
                                      : `/document/${param?.documentId}/create-document`
                                    : '#'
                                }
                                className="w-64 h-36 flex-1"
                              >
                                <div className="w-full h-full flex flex-col items-center justify-center rounded-2xl bg-gray-50 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                                  <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-4">
                                    <DocumentIcon className="w-6 h-6 text-gray-300" />
                                  </div>
                                  <h3 className="text-base font-medium text-dark-300 mb-1">
                                    Create with editor
                                  </h3>
                                  <p className="text-xs font-semibold text-gray-400 mt-1 text-center">
                                    Create a document in our inbuilt editor
                                  </p>
                                </div>
                              </Link>
                            </div>
                          </div>
                          <DialogFooter>
                            <p className="text-sm font-medium text-red-200">
                              {data?.record?.assignees?.length === 0 &&
                                '* At least one assignee is required to create a document.'}
                            </p>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </>
                  ) : undefined
                }
              />
            </div>
          ) : (
            ''
          )}
        </div>
        {!loadingData ? (
          <div className=" flex-1">
            {activeTab === 0 ? (
              <>
                <div className="border border-grey-100 p-2 rounded-lg">
                  {((data?.record?.next_review_date &&
                    hasAccess(AccessActions.IsDocumentAdmin, user)) ||
                    (user?.id &&
                      data?.record?.assignees?.some((e) => e.id === user.id) &&
                      getDaysUntilReview(data.record.next_review_date) !==
                        null)) && (
                    <div className="flex gap-2 items-center bg-white-150 px-4 py-3 mb-4 rounded-xl">
                      <ReviewDateBanner
                        reviewDate={data.record.next_review_date}
                      />
                      <Dialog
                        open={reviewPeriodModal}
                        onOpenChange={setReviewPeriodModal}
                      >
                        <DialogTrigger asChild>
                          <LinkButton size="small" text="Start Review" />
                        </DialogTrigger>
                        <ReviewDocumentModal
                          setReviewPeriodModal={setReviewPeriodModal}
                          title={data?.record?.title || ''}
                          version={
                            data?.record?.document_version?.version_number || 0
                          }
                          extension={
                            data?.record?.document_version?.file_extension || ''
                          }
                          filePath={
                            data?.record?.document_version?.file_path || ''
                          }
                          documentVersionId={
                            data?.record?.document_version?.id || ''
                          }
                          publishDate={data?.record?.next_review_date || ''}
                          refetchDocumentData={refetchDocumentData}
                        />
                      </Dialog>
                    </div>
                  )}

                  <div className="flex bg-white items-start justify-between  pb-2">
                    <div className="p-2 flex flex-col gap-3">
                      <DetailsText
                        label="Doc ID"
                        value={
                          truncateWithEllipsis(
                            data?.record?.doc_id as string,
                            20,
                          ) || '-'
                        }
                        newLine={true}
                      />
                      <DetailsText
                        label="Origin"
                        value={data?.record?.origin || '-'}
                        newLine={true}
                      />
                    </div>
                    <div className="p-2 flex flex-col gap-3">
                      <DetailsText
                        label="Department"
                        value={
                          truncateWithEllipsis(
                            data?.record?.department?.name as string,
                            20,
                          ) || '-'
                        }
                        newLine={true}
                      />
                      <DetailsText
                        label="Category"
                        value={
                          truncateWithEllipsis(
                            data?.record?.category?.name as string,
                            20,
                          ) || '-'
                        }
                        newLine={true}
                      />

                      {/* <DetailsText
                      label="Approver"
                      value={
                        data?.record?.approvers?.length &&
                        data?.record?.approvers?.length > 0
                          ? data?.record?.approvers
                          : '-'
                      }
                      multiValue
                    /> */}
                    </div>
                    <div className="p-2 flex flex-col gap-3">
                      <DetailsText
                        label="Assignee"
                        value={
                          data?.record?.assignees?.length &&
                          data?.record?.assignees?.length > 0
                            ? data?.record?.assignees
                            : '-'
                        }
                        newLine={true}
                        multiValue={
                          data?.record?.assignees?.length &&
                          data?.record?.assignees?.length > 0
                            ? true
                            : false
                        }
                      />
                      <DetailsText
                        label="Approver"
                        value={
                          data?.record?.approvers?.length &&
                          data?.record?.approvers?.length > 0
                            ? data?.record?.approvers
                            : '-'
                        }
                        newLine={true}
                        multiValue={
                          data?.record?.approvers?.length &&
                          data?.record?.approvers?.length > 0
                            ? true
                            : false
                        }
                      />
                      {/* <DetailsText
                      label="Review period"
                      value={
                        showReviewPeriod(
                          data?.record?.review_period as number,
                        ) || '-'
                      }
                    /> */}
                    </div>
                    <div className="p-2 flex flex-col gap-3">
                      <DetailsText
                        label="Process"
                        value={data?.record?.processes || '-'}
                        multiValue
                        newLine={true}
                      />
                      <DetailsText
                        label="Next review"
                        newLine={true}
                        value={
                          formatDate(
                            data?.record?.next_review_date as string,
                            false,
                          ) || '-'
                        }
                      />
                      {/* <DetailsText
                      label="Review period"
                      value={
                        showReviewPeriod(
                          data?.record?.review_period as number,
                        ) || '-'
                      }
                    />
                    <DetailsText
                      label="Next review"
                      value={
                        formatDate(
                          data?.record?.next_review_date as string,
                          false,
                        ) || '-'
                      }
                    /> */}
                    </div>
                    {hasAccess(AccessActions.IsDocumentAdmin, user) && (
                      <div className="flex items-center gap-3">
                        <Dialog
                          open={openEditModal}
                          onOpenChange={setOpenEditModal}
                        >
                          <DialogTrigger asChild>
                            <SecondaryButton
                              size="medium"
                              icon={
                                <EditIcon color="#016366" className="h-5 w-5" />
                              }
                              text="Edit"
                            />
                          </DialogTrigger>
                          <CreateDocumentModal
                            prefill={data?.record}
                            edit
                            open={openEditModal}
                            setOpenEdit={setOpenEditModal}
                            reFetch={refetchDocumentData}
                          />
                        </Dialog>

                        <Dialog>
                          <DialogTrigger asChild>
                            <DeleteButton />
                          </DialogTrigger>
                          <DeleteModal
                            title="Delete"
                            infoText="Are you sure?"
                            btnText="Delete"
                            onClick={() => {
                              async function fetch() {
                                await deleteData(
                                  accessToken as string,
                                  `documents/${data?.record.id}`,
                                );
                              }
                              fetch();
                            }}
                            dialogContentClass="min-w-[28.5rem]"
                          >
                            <div className="p-2 border flex flex-col gap-4 border-white-300 bg-white-100 px-2.5 py-2 rounded-lg">
                              <div className="flex justify-between items-center">
                                <div className="text-sm font-medium leading-5 text-grey-300">
                                  ID
                                </div>
                                <div className="text-base font-medium leading-6 text-dark-300">
                                  {data?.record.doc_id || '-'}
                                </div>
                              </div>
                              <div className="flex justify-between items-center">
                                <div className="text-sm font-medium leading-5 text-grey-300">
                                  Document name
                                </div>
                                <div className="text-base font-medium leading-6 text-dark-300">
                                  {data?.record.title
                                    ? truncateWithEllipsis(
                                        data?.record.title as string,
                                        30,
                                      )
                                    : '-'}
                                </div>
                              </div>
                            </div>
                          </DeleteModal>
                        </Dialog>
                      </div>
                    )}
                    {!hasAccess(AccessActions.IsDocumentAdmin, user) &&
                      hasAccess(AccessActions.IsDocumentEditor, user) && (
                        <div className="flex items-center gap-3">
                          <Dialog
                            open={openEditModal}
                            onOpenChange={setOpenEditModal}
                          >
                            <DialogTrigger asChild>
                              <SecondaryButton
                                size="medium"
                                icon={
                                  <EditIcon
                                    color="#016366"
                                    className="h-5 w-5"
                                  />
                                }
                                text="Edit"
                              />
                            </DialogTrigger>
                            <CreateDocumentModal
                              prefill={data?.record}
                              edit
                              open={openEditModal}
                              setOpenEdit={setOpenEditModal}
                              reFetch={refetchDocumentData}
                            />
                          </Dialog>
                        </div>
                      )}
                  </div>
                  {data?.record?.document_version && (
                    <div className="pt-2 border-t border-grey-50">
                      <div className="flex items-center justify-between py-2 px-3 ">
                        <div className="flex gap-4">
                          <div className="bg-gray-50 p-2 w-12 h-12 rounded-md">
                            <DocumentIcon className="w-8 h-8 text-gray-300" />
                          </div>

                          <div>
                            {[
                              'pdf',
                              'docx',
                              'doc',
                              'html',
                              'jpg',
                              'png',
                              'jpeg',
                            ].includes(
                              data?.record?.document_version?.file_extension,
                            ) ? (
                              <Dialog
                                open={openDocumentModal}
                                onOpenChange={setOpenDocumentModal}
                              >
                                <DialogTrigger>
                                  <LinkButton
                                    text={
                                      data?.record?.document_version
                                        ?.file_extension === 'html'
                                        ? data?.record?.title
                                        : data?.record?.document_version.file_path
                                            .split('/')
                                            .pop()
                                    }
                                    size="medium"
                                  />
                                </DialogTrigger>
                                {openDocumentModal && (
                                  <DocumentViewModal
                                    title={data.record.title}
                                    filePath={
                                      data?.record?.document_version?.file_path
                                    }
                                    extension={
                                      data?.record?.document_version
                                        ?.file_extension as
                                        | 'html'
                                        | 'pdf'
                                        | 'docx'
                                        | 'doc'
                                        | 'png'
                                        | 'jpeg'
                                        | 'jpg'
                                    }
                                    dialogClass="min-w-[95%]"
                                  />
                                )}
                              </Dialog>
                            ) : (
                              data?.record?.document_version?.file_path
                                .split('/')
                                .pop()
                            )}
                            <p className="text-grey-300 font-medium text-sm mt-1">
                              Version:{' '}
                              <span className="text-black">
                                {data?.record?.document_version?.version_number}
                              </span>
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          {data?.record?.document_version?.file_extension ===
                          'html' ? (
                            !data?.record?.approval ? (
                              <>
                                <Dialog
                                  open={editPublishedVersion}
                                  onOpenChange={setEditPublishedVersion}
                                >
                                  <DialogTrigger>
                                    <Tooltip>
                                      <TooltipTrigger>
                                        <div className="w-10 h-10 flex items-center justify-center  bg-white-200 rounded-full hover:bg-white-300 cursor-pointer">
                                          <EditIcon className="h-6 w-6" />
                                        </div>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <div className=" text-sm text-dark-300">
                                          Edit
                                        </div>
                                      </TooltipContent>
                                    </Tooltip>
                                  </DialogTrigger>

                                  <ConfirmModal
                                    title={'Edit Document'}
                                    infoText={
                                      'Are you sure you want to edit the document for this version?'
                                    }
                                    btnText={'Confirm'}
                                    onClick={() =>
                                      router.push(
                                        `/document/${param.documentId}/create-document?file_path=${data?.record?.document_version?.file_path}`,
                                      )
                                    }
                                    dialogClass="min-w-[45.438rem]"
                                  />
                                </Dialog>

                                <Tooltip>
                                  <TooltipTrigger>
                                    <div
                                      className="w-10 h-10 flex items-center justify-center  bg-white-200 rounded-full hover:bg-white-300 cursor-pointer"
                                      onClick={() =>
                                        handleDownloadDocument(
                                          data?.record?.document_version
                                            .file_path,
                                          data?.record?.title,
                                          data?.record?.document_version
                                            .version_number,
                                          data?.record?.document_version
                                            .file_extension,
                                        )
                                      }
                                    >
                                      <Download className="h-6 w-6" />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <div className="text-sm text-dark-300">
                                      Download
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </>
                            ) : (
                              ''
                            )
                          ) : (
                            <Tooltip>
                              <TooltipTrigger>
                                <div
                                  className="w-10 h-10 flex items-center justify-center  bg-white-200 rounded-full hover:bg-white-300 cursor-pointer"
                                  onClick={() =>
                                    handleDownloadDocument(
                                      data?.record?.document_version.file_path,
                                      data?.record?.title,
                                      data?.record?.document_version
                                        .version_number,
                                    )
                                  }
                                >
                                  <Download className="h-6 w-6" />
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="text-sm text-dark-300">
                                  Download
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="mt-6">
                  {/* {(hasAccess(AccessActions.CanUploadDocument, user) ||
                    (user?.id &&
                      data?.record?.assignees.find(
                        (e) => e.id === user?.id,
                      ))) &&
                    !data?.record?.approval && (
                      <>
                        <div className="flex justify-between mb-4">
                          <div className="text-sm leading-5 font-medium text-black ">
                            Document actions:
                          </div>
                          {!haveApproverAssignee ? (
                            <div className="flex items-center gap-2">
                              <div>
                                <InfoCircle
                                  height="20"
                                  width="20"
                                  color="#E05252"
                                />
                              </div>
                              <div className="text-sm leading-5 font-medium text-red-300">
                                Document upload / creation is not allowed
                                without adding assignees and approvers
                              </div>
                            </div>
                          ) : (
                            ''
                          )}
                        </div>

                        <div className="flex gap-6">
                          <Dialog
                            open={openUploadModal}
                            onOpenChange={setOpenUploadModal}
                          >
                            <DialogTrigger asChild>
                              <div
                                className={cn(
                                  'flex items-center rounded-lg flex-1 p-4 gap-4 border border-grey-100 text-base text-dark-300 leading-6 font-medium cursor-pointer hover:bg-white-150',
                                  !haveApproverAssignee
                                    ? ' opacity-50 cursor-not-allowed'
                                    : '',
                                )}
                                onClick={(e) => {
                                  if (haveApproverAssignee) {
                                    setOpenUploadModal(true);
                                  } else {
                                    e.preventDefault();
                                    e.stopPropagation();
                                  }
                                }}
                              >
                                <div>
                                  <UploadIcon />
                                </div>
                                Upload document
                              </div>
                            </DialogTrigger>
                            <UploadModal
                              setOpenUploadModal={setOpenUploadModal}
                              refetchDocumentData={refetchDocumentData}
                              openUploadModal={openUploadModal}
                              currentVersion={
                                data?.record?.document_version?.version_number
                              }
                            />
                          </Dialog>
                          <div
                            className={cn(
                              'flex items-center rounded-lg flex-1 p-4 gap-4 border border-grey-100 text-base text-dark-300 leading-6 font-medium cursor-pointer hover:bg-white-150',
                              !haveApproverAssignee
                                ? ' opacity-50 cursor-not-allowed'
                                : '',
                            )}
                            onClick={() => {
                              if (haveApproverAssignee) {
                                router.push(
                                  `/document/${param?.documentId}/create-document`,
                                );
                              } else {
                              }
                            }}
                          >
                            <div>
                              <DocumentIcon />
                            </div>
                            Create new document
                          </div>
                        </div>
                      </>
                    )} */}
                  {data?.record?.approval && (
                    <>
                      <div className="text-lg leading-5 font-medium text-black">
                        Document Approval
                      </div>
                      <p className="text-sm text-gray-400 mt-2">
                        Manage the document version sent for approval
                      </p>
                      <div className="flex items-center justify-between p-4 bg-white-100 border border-white-300 rounded-lg mt-5">
                        <div className="flex-1">
                          {/* {data?.record?.approval?.payload?.file_extension ===
                          'html'
                            ? data?.record?.title
                            : data?.record?.approval?.payload?.file_path
                                ?.split('/')
                                .pop()} */}
                          <div className="flex gap-4">
                            <div className="bg-gray-50 p-2 w-12 h-12 rounded-md">
                              <DocumentIcon className="w-8 h-8 text-gray-300" />
                            </div>

                            <div>
                              {[
                                'pdf',
                                'docx',
                                'doc',
                                'html',
                                'jpg',
                                'png',
                                'jpeg',
                              ].includes(
                                data?.record?.approval?.payload?.file_extension,
                              ) ? (
                                <Dialog>
                                  <DialogTrigger>
                                    <LinkButton
                                      text={data?.record?.title}
                                      size="medium"
                                    />
                                  </DialogTrigger>
                                  <DocumentViewModal
                                    title={data.record.title}
                                    filePath={
                                      data?.record?.approval?.payload?.file_path
                                    }
                                    extension={
                                      data?.record?.approval?.payload
                                        ?.file_extension as
                                        | 'html'
                                        | 'pdf'
                                        | 'docx'
                                        | 'doc'
                                        | 'png'
                                        | 'jpeg'
                                        | 'jpg'
                                    }
                                    dialogClass="min-w-[95%]"
                                  />
                                </Dialog>
                              ) : (
                                data?.record?.approval?.payload?.file_path
                                  .split('/')
                                  .pop()
                              )}
                              <p className="text-grey-300 font-medium text-sm mt-1">
                                Version:{' '}
                                <span className="text-black">
                                  {data?.record?.approval?.payload?.version}
                                </span>
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          {data?.record?.approval?.payload?.file_extension ===
                          'html' ? (
                            hasAccess(AccessActions.IsDocumentAdmin, user) ||
                            (hasAccess(AccessActions.IsDocumentEditor, user) &&
                              user?.id &&
                              data?.record?.assignees.find(
                                (e) => e.id === user?.id,
                              )) ? (
                              <>
                                <Dialog
                                  open={editVersion}
                                  onOpenChange={setEditVersion}
                                >
                                  <DialogTrigger>
                                    <Tooltip>
                                      <TooltipTrigger>
                                        <div className="w-10 h-10 flex items-center justify-center  bg-white-200 rounded-full hover:bg-white-300 cursor-pointer">
                                          <EditIcon className="h-6 w-6" />
                                        </div>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <div className=" text-sm text-dark-300">
                                          Edit
                                        </div>
                                      </TooltipContent>
                                    </Tooltip>
                                  </DialogTrigger>

                                  <ConfirmModal
                                    title={'Edit Document'}
                                    infoText={
                                      'Are you sure you want to edit the document for this version?'
                                    }
                                    btnText={'Confirm'}
                                    onClick={() =>
                                      router.push(
                                        `/document/${param.documentId}/create-document?file_path=${data?.record?.approval?.payload?.file_path}`,
                                      )
                                    }
                                    dialogClass="min-w-[45.438rem]"
                                  />
                                </Dialog>
                                <Tooltip>
                                  <TooltipTrigger>
                                    <div
                                      className="w-10 h-10 flex items-center justify-center  bg-white-200 rounded-full hover:bg-white-300 cursor-pointer"
                                      onClick={() =>
                                        handleDownloadDocument(
                                          data?.record?.approval?.payload
                                            ?.file_path,
                                          data?.record?.title,
                                          data?.record?.approval?.payload
                                            ?.version,
                                          data?.record?.approval?.payload
                                            ?.file_extension,
                                        )
                                      }
                                    >
                                      <Download className="h-6 w-6" />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <div className="text-sm text-dark-300">
                                      Download
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </>
                            ) : (
                              ''
                            )
                          ) : (
                            <Tooltip>
                              <TooltipTrigger>
                                <div
                                  className="w-10 h-10 flex items-center justify-center  bg-white-200 rounded-full hover:bg-white-300 cursor-pointer"
                                  onClick={() =>
                                    handleDownloadDocument(
                                      data?.record?.approval?.payload
                                        ?.file_path,
                                      data?.record?.title,
                                      data?.record?.approval?.payload?.version,
                                      data?.record?.approval?.payload
                                        ?.file_extension,
                                    )
                                  }
                                >
                                  <Download className="h-6 w-6" />
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="text-sm text-dark-300">
                                  Download
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          )}

                          {hasAccess(AccessActions.IsDocumentAdmin, user) ||
                          (hasAccess(AccessActions.IsDocumentEditor, user) &&
                            user?.id &&
                            data?.record?.assignees.find(
                              (e) => e.id === user?.id,
                            )) ? (
                            <Dialog
                              open={deleteVersion}
                              onOpenChange={setDeleteVersion}
                            >
                              <DialogTrigger>
                                <Tooltip>
                                  <TooltipTrigger>
                                    <div className="w-10 h-10 flex items-center justify-center  bg-white-200 rounded-full hover:bg-white-300 cursor-pointer">
                                      <DeleteIcon className="h-6 w-6" />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent className="">
                                    <div className=" text-sm text-dark-300">
                                      Delete
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </DialogTrigger>
                              <DeleteModal
                                title="Delete"
                                infoText="Are you sure you want to delete this version? "
                                btnText="Delete"
                                onClick={() => {
                                  async function fetch() {
                                    await deleteApproval(
                                      accessToken as string,
                                      `approvals/${data?.record?.approval?.id}`,
                                    );
                                  }
                                  fetch();
                                }}
                                dialogContentClass="min-w-[33.375rem]"
                                btnLoading={deleteApprovalLoading}
                              ></DeleteModal>
                            </Dialog>
                          ) : (
                            ''
                          )}
                        </div>
                      </div>
                    </>
                  )}

                  {/* {data?.record?.approval && (
                    <div className="p-2 bg-white-100 border border-white-300 rounded-lg mt-3">
                      <div className="flex-1 p-2 pb-4 mb-2 border-b border-white-300">
                        Pending approvals{' '}
                      </div>
                      <div className="flex items-center justify-between py-2 px-3">
                        <div className="flex items-center  gap-4  justify-center py-2">
                          <div className="flex gap-1 text-base text-dark-300 leading-6 font-medium">
                            <span className="text-grey-300">Version: </span>
                            {data?.record?.approval.version}
                          </div>
                          {[
                            'pdf',
                            'docx',
                            'doc',
                            'html',
                            'jpg',
                            'png',
                            'jpeg',
                          ].includes(data?.record?.approval?.file_extension) ? (
                            <Dialog>
                              <DialogTrigger>
                                <LinkButton text="View" size="medium" />
                              </DialogTrigger>
                              <DocumentViewModal
                                title={data.record.title}
                                filePath={data?.record?.approval?.file_path}
                                extension={
                                  data?.record?.approval?.file_extension as
                                    | 'html'
                                    | 'pdf'
                                    | 'docx'
                                    | 'doc'
                                    | 'png'
                                    | 'jpeg'
                                    | 'jpg'
                                }
                                dialogClass="min-w-[95%]"
                              />
                            </Dialog>
                          ) : (
                            ''
                          )}
                        </div>
                        {user?.id &&
                          data?.record?.approvers.find(
                            (e) => e.id === user?.id,
                          ) && (
                            <div className="flex items-center gap-3">
                              <Dialog
                                open={openJustificationModal}
                                onOpenChange={setOpenJustifcationModal}
                              >
                                <DialogTrigger>
                                  <TertiaryButton size="medium" text="Reject" />
                                </DialogTrigger>
                                <JustificationModal
                                  setOpenJustificationModal={
                                    setOpenJustifcationModal
                                  }
                                  approvalId={
                                    data?.record?.approval?.id as string
                                  }
                                  handleRejectMfa={handleRejectMfa}
                                  refetch={refetchDocumentData}
                                />
                              </Dialog>
                              <PrimaryButton
                                onClick={() => handleApprove()}
                                isLoading={approvalLoading}
                                size="medium"
                                text="Approve"
                              />
                              
                            </div>
                          )}
                      </div>
                    </div>
                  )} */}

                  <Dialog open={otpModal} onOpenChange={setOtpModal}>
                    {otpModal && (
                      <OtpModal
                        setOtpModal={setOtpModal}
                        sessionId={mfaSessionId}
                        refetchDocumentData={refetchDocumentData}
                      />
                    )}
                  </Dialog>

                  {/* {data?.record?.document_version && (
                    <div className="bg-white-100 border border-white-300 rounded-lg mt-3 p-2">
                      <div className="flex-1 p-2 pb-4 mb-2 border-b border-white-300">
                        Published version{' '}
                      </div>
                      <div className="flex items-center justify-between py-2 px-3 ">
                        <div className="flex-1">
                          {data?.record?.document_version?.file_extension ===
                          'html'
                            ? data?.record?.title
                            : data?.record?.document_version.file_path
                                .split('/')
                                .pop()}

                          {[
                            'pdf',
                            'docx',
                            'doc',
                            'html',
                            'jpg',
                            'png',
                            'jpeg',
                          ].includes(
                            data?.record?.document_version?.file_extension,
                          ) ? (
                            <Dialog
                              open={openDocumentModal}
                              onOpenChange={setOpenDocumentModal}
                            >
                              <DialogTrigger className="ml-4">
                                <LinkButton text="View" size="medium" />
                              </DialogTrigger>
                              {openDocumentModal && (
                                <DocumentViewModal
                                  title={data.record.title}
                                  filePath={
                                    data?.record?.document_version?.file_path
                                  }
                                  extension={
                                    data?.record?.document_version
                                      ?.file_extension as
                                      | 'html'
                                      | 'pdf'
                                      | 'docx'
                                      | 'doc'
                                      | 'png'
                                      | 'jpeg'
                                      | 'jpg'
                                  }
                                  dialogClass="min-w-[95%]"
                                />
                              )}
                            </Dialog>
                          ) : (
                            ''
                          )}
                        </div>
                        <div className="flex items-center gap-4">
                          {data?.record?.document_version?.file_extension ===
                          'html' ? (
                            !data?.record?.approval ? (
                              <>
                                <Tooltip>
                                  <TooltipTrigger>
                                    <div
                                      className="w-10 h-10 flex items-center justify-center  bg-white-200 rounded-full hover:bg-white-300 cursor-pointer"
                                      onClick={() =>
                                        router.push(
                                          `/document/${param.documentId}/create-document?file_path=${data?.record?.document_version?.file_path}`,
                                        )
                                      }
                                    >
                                      <EditIcon className="h-6 w-6" />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <div className=" text-sm text-dark-300">
                                      Edit
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                                <Tooltip>
                                  <TooltipTrigger>
                                    <div
                                      className="w-10 h-10 flex items-center justify-center  bg-white-200 rounded-full hover:bg-white-300 cursor-pointer"
                                      onClick={() =>
                                        handleDownloadDocument(
                                          data?.record?.document_version
                                            .file_path,
                                          data?.record?.title,
                                          data?.record?.document_version
                                            .version_number,
                                          data?.record?.document_version
                                            .file_extension,
                                        )
                                      }
                                    >
                                      <Download className="h-6 w-6" />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <div className="text-sm text-dark-300">
                                      Download
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </>
                            ) : (
                              ''
                            )
                          ) : (
                            <Tooltip>
                              <TooltipTrigger>
                                <div
                                  className="w-10 h-10 flex items-center justify-center  bg-white-200 rounded-full hover:bg-white-300 cursor-pointer"
                                  onClick={() =>
                                    handleDownloadDocument(
                                      data?.record?.document_version.file_path,
                                      data?.record?.title,
                                      data?.record?.document_version
                                        .version_number,
                                    )
                                  }
                                >
                                  <Download className="h-6 w-6" />
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="text-sm text-dark-300">
                                  Download
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          )}
                        </div>
                      </div>
                    </div>
                  )} */}
                </div>

                {/* ---------- */}

                {data?.record?.approval && (
                  <>
                    <div className="p-2 bg-white-100 border border-white-300 rounded-lg mt-3">
                      <div className="flex justify-between p-2 pb-4 pt-2 mb-2 border-b border-white-300">
                        <div className="flex gap-4">
                          Pending approvals{' '}
                          <Status
                            type={
                              data?.record?.approval?.status.toLowerCase() as string
                            }
                          />
                        </div>
                        {hasAccess(AccessActions.CanEditDocument, user) &&
                          data?.record?.approval?.status === 'Pending' && (
                            <Dialog
                              open={editApproversModal}
                              onOpenChange={setEditApproversModal}
                            >
                              <DialogTrigger asChild>
                                <LinkButton
                                  size="medium"
                                  icon={
                                    <EditIcon
                                      color="#016366"
                                      className="h-5 w-5"
                                    />
                                  }
                                  text="Edit Approvers"
                                />
                              </DialogTrigger>
                              <EditApproversModal
                                setEditApproversModal={setEditApproversModal}
                                approvalId={
                                  data?.record?.approval?.id as string
                                }
                                existingApprovers={
                                  data?.record?.approval?.approvers
                                }
                                existingApprovalType={
                                  data?.record?.approval?.flow
                                }
                                refetch={refetchDocumentData}
                                assignees={data?.record?.assignees}
                              />
                            </Dialog>
                          )}
                      </div>

                      <div className="px-3">
                        {data?.record?.approval?.approvers.map(
                          (approver: any, index: number) => {
                            const flow = data?.record?.approval?.flow;
                            const isSequential = flow === 'Sequential';

                            // Check if any prior approver has not approved
                            const isBlocked =
                              isSequential &&
                              data.record.approval?.approvers
                                .slice(0, index)
                                .some((a: any) => a.status !== 'Approved');

                            return (
                              <div
                                className="border-b border-white-300 last:border-none"
                                key={index}
                              >
                                <div className="py-4 flex items-center justify-between gap-2 text-base text-dark-300 leading-6 font-medium">
                                  <div className="flex items-center gap-3">
                                    <p>
                                      {index + 1}. {approver?.user.name}
                                    </p>
                                    {approver?.status === 'Approved' ? (
                                      <>
                                        <div className="h-8 w-8 flex items-center justify-center bg-green-50 rounded-full">
                                          <TickIcon
                                            height="16"
                                            width="16"
                                            color="#309665"
                                          />
                                        </div>
                                        <p className="text-green-200 text-sm">
                                          {approver?.status}
                                        </p>
                                      </>
                                    ) : approver?.status === 'Pending' ? (
                                      <>
                                        <div className="h-8 w-8 flex items-center justify-center bg-yellow-50 rounded-full">
                                          <WarningIcon
                                            height="16"
                                            width="16"
                                            color="#F19413"
                                          />
                                        </div>
                                        <p className="text-yellow-200 text-sm">
                                          {approver?.status}
                                        </p>
                                      </>
                                    ) : (
                                      <>
                                        <div className="h-8 w-8 flex items-center justify-center bg-red-50 rounded-full">
                                          <WarningIcon
                                            height="16"
                                            width="16"
                                            color="#F19413"
                                          />
                                        </div>
                                        <p className="text-red-200 text-sm">
                                          {approver?.status}
                                        </p>
                                        <br />
                                      </>
                                    )}
                                  </div>

                                  {user?.id &&
                                    approver?.status == 'Pending' &&
                                    user?.id === approver.user.id &&
                                    !isBlocked && (
                                      <div className="flex items-center gap-3">
                                        <Dialog
                                          open={openJustificationModal}
                                          onOpenChange={
                                            setOpenJustifcationModal
                                          }
                                        >
                                          <DialogTrigger>
                                            <TertiaryButton
                                              size="medium"
                                              text="Reject"
                                            />
                                          </DialogTrigger>
                                          <JustificationModal
                                            setOpenJustificationModal={
                                              setOpenJustifcationModal
                                            }
                                            approvalId={
                                              data?.record?.approval
                                                ?.id as string
                                            }
                                            handleRejectMfa={handleRejectMfa}
                                            refetch={refetchDocumentData}
                                          />
                                        </Dialog>
                                        <PrimaryButton
                                          onClick={() => handleApprove()}
                                          isLoading={approvalLoading}
                                          size="medium"
                                          text="Approve"
                                        />
                                      </div>
                                    )}

                                  {approver?.status !== 'Pending' && (
                                    <p className="text-grey-300 font-medium text-base leading-6 whitespace-nowrap">
                                      {approver?.last_modified_on
                                        ? formatDate(
                                            approver?.last_modified_on,
                                            true,
                                          )
                                        : '-'}
                                    </p>
                                  )}
                                </div>
                                {approver?.remark && (
                                  <p className="text-sm -mt-1 mb-4">
                                    <span className=" text-gray-400">
                                      Remarks:
                                    </span>{' '}
                                    {approver?.remark}
                                  </p>
                                )}
                              </div>
                            );
                          },
                        )}
                      </div>
                    </div>

                    {(hasAccess(AccessActions.CanUploadDocument, user) ||
                      (user?.id &&
                        data?.record?.assignees?.some(
                          (e) => e.id === user?.id,
                        ))) &&
                      data.record.approval.status === 'Approved' && (
                        <>
                          <div className="p-2 bg-white-100 border border-white-300 rounded-lg mt-3">
                            <div className="flex items-center p-2 pb-4 border-b border-white-300">
                              Set Review Schedule{' '}
                              <Tooltip>
                                <TooltipTrigger>
                                  <div
                                    className="w-10 h-10 flex items-center justify-center rounded-full cursor-pointer"
                                    onClick={() => console.log('first')}
                                  >
                                    <InfoCircle />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <div className="text-sm text-dark-300">
                                    Set the recurring time period after which
                                    the document should be reviewed.
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                            </div>
                            <div className="flex items-center gap-4 justify-between py-4 px-3 ">
                              <div className="flex gap-4 items-center text-base text-dark-300 leading-6 font-medium">
                                <p className="text-grey-300 font-medium text-base leading-6 whitespace-nowrap">
                                  Review Period
                                  <span className="text-red-200">*</span>
                                </p>
                                <Select
                                  value={reviewPeriod}
                                  onValueChange={(value: ReviewPeriod) => {
                                    setReviewPeriod(value);
                                    setNextReviewDate(
                                      moment(publishDate)
                                        .add(periodMap[value], 'months')
                                        .format('YYYY-MM-DD'),
                                    );
                                  }}
                                >
                                  <SelectTrigger
                                    className={'w-[240px]'}
                                    id="review"
                                  >
                                    <SelectValue placeholder="---" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="Monthly">
                                      Monthly
                                    </SelectItem>
                                    <SelectItem value="Quarterly">
                                      Quarterly
                                    </SelectItem>
                                    <SelectItem value="Half Yearly">
                                      Half Yearly
                                    </SelectItem>
                                    <SelectItem value="Annually">
                                      Annually
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                            {!data?.record?.document_version && (
                              <div className="flex items-center gap-4 justify-between pt-4 pb-2 px-3 border-t border-white-300">
                                <div className="flex gap-4 text-base text-dark-300 leading-6 font-medium">
                                  <div className="flex items-center">
                                    <Tooltip>
                                      <TooltipTrigger>
                                        <div
                                          className="w-10 h-10 flex items-center justify-center rounded-fullcursor-pointer"
                                          onClick={() => console.log('first')}
                                        >
                                          <InfoCircle />
                                        </div>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <div className="text-sm text-dark-300">
                                          Optionally, set a specific date for
                                          the next review. If selected, the
                                          recurring review cycle will be counted
                                          from this date instead of the
                                          document&apos;s publish date.
                                        </div>
                                      </TooltipContent>
                                    </Tooltip>
                                    <h3 className="text-base mt-0.5 font-medium text-approval-text-primary">
                                      Pick a specific date for the next review
                                    </h3>
                                  </div>

                                  <ToggleSwitch
                                    initialState={customReviewDate}
                                    onChange={(state) => {
                                      setCustomReviewDate(state);

                                      if (!state) {
                                        setPublishDate(
                                          moment().format('YYYY-MM-DD'),
                                        );
                                      }
                                    }}
                                  />
                                </div>
                                <div className="flex gap-4 items-center text-base text-dark-300 leading-6 font-medium">
                                  <p className="text-grey-300 font-medium text-base leading-6 whitespace-nowrap">
                                    Next Review Date
                                  </p>
                                  <Calendar
                                    className={`${
                                      !customReviewDate
                                        ? 'bg-white-150'
                                        : 'bg-slate-50'
                                    } w-full`}
                                    selectedDate={
                                      (customReviewDate && nextReviewDate) || ''
                                    }
                                    disabled={!customReviewDate}
                                    onDateChange={(date) => {
                                      if (date) {
                                        setNextReviewDate(
                                          moment(date as string).format(
                                            'YYYY-MM-DD',
                                          ),
                                        );
                                      } else {
                                        setNextReviewDate('');
                                      }
                                    }}
                                  />
                                </div>
                              </div>
                            )}
                          </div>
                          <div className="flex gap-4 items-center mt-3">
                            <div className="flex-1 bg-green-50 rounded-md">
                              <div className="flex gap-2 px-4 py-2 items-center text-base text-dark-300 leading-6 font-medium">
                                <div
                                  className={cn(
                                    'h-5 w-5 flex items-center justify-center bg-green-200 rounded-full',
                                  )}
                                >
                                  <CheckIcon height="16" width="16" />
                                </div>
                                <p className="text-base font-medium">
                                  Next review scheduled for:{' '}
                                  {reviewPeriod ? (
                                    <>
                                      {nextReviewDate
                                        ? moment(nextReviewDate).format(
                                            'MMM D YYYY',
                                          )
                                        : moment(publishDate)
                                            .add(
                                              periodMap[
                                                reviewPeriod as ReviewPeriod
                                              ] || 0,
                                              'months',
                                            )
                                            .format('MMM D YYYY')}
                                      {} (repeats {reviewPeriod} from{' '}
                                      {moment(publishDate).format('MMM D YYYY')}
                                      )
                                    </>
                                  ) : (
                                    '---'
                                  )}
                                </p>
                              </div>
                            </div>
                            <PrimaryButton
                              text="Publish"
                              size="medium"
                              onClick={() => handlePublish()}
                              disabled={!reviewPeriod}
                              isLoading={isLoading}
                            />
                          </div>
                        </>
                      )}
                  </>
                )}

                {/* ---------- */}
                <div className="text-lg leading-5 font-medium text-black mt-6">
                  Comments
                </div>
                <div className="mt-5 min-h-64 bg-white-150 rounded-lg p-5">
                  {!documentComments?.records?.length ? (
                    <div className="w-full h-24 bg-white-100 border border-white-300 rounded-lg p-4">
                      <p className="text-base text-dark-300 leading-6 font-medium">
                        No response has been added yet
                      </p>
                    </div>
                  ) : (
                    documentComments?.records?.map((e, i) => (
                      <div
                        className="px-4 mb-2 py-4 border border-white-300 rounded-lg bg-white-100"
                        key={i}
                      >
                        <div className="flex mb-1 gap-2 items-center text-base leading-6 font-medium text-dark-300">
                          <div>{e.created_by.full_name}</div>
                          <div className="text-grey-300">
                            {data?.record?.approvers?.find(
                              (x) => x.id === e.created_by.id,
                            )
                              ? 'Approver'
                              : data?.record?.assignees?.find(
                                  (x) => x.id === e.created_by.id,
                                )
                              ? 'Assignee'
                              : 'Commenter'}
                          </div>
                        </div>
                        <div className="text-sm flex items-center mb-1 gap-2 leading-5 font-semibold text-grey-200">
                          <span>{moment(e.created_on).format('hh:mm')},</span>{' '}
                          <span>
                            {moment(e.created_on).format('MMM Do, YYYY')}
                          </span>
                        </div>
                        <p
                          onClick={(e) => e.preventDefault()}
                          dangerouslySetInnerHTML={{
                            __html: e?.description,
                          }}
                          className="text-base font-medium leading-6 text-dark-300"
                        />
                      </div>
                    ))
                  )}
                </div>
                <div className="flex items-center justify-end mt-3">
                  <Dialog open={openResponse} onOpenChange={setOpenResponse}>
                    <DialogTrigger asChild>
                      <PrimaryButton text="Add response" size="medium" />
                    </DialogTrigger>
                    <AddResponseModal
                      setOpenResponse={setOpenResponse}
                      refetchComments={refetchComments}
                    />
                  </Dialog>
                </div>
              </>
            ) : activeTab === 1 ? (
              versionLoading ? (
                <Loader className="h-[400px]" />
              ) : (
                <>
                  {versions?.records?.length &&
                  versions?.records?.length > 0 ? (
                    versions?.records.map((e, i) => (
                      <VersionCard
                        versionData={e}
                        handleDownloadDocument={handleDownloadDocument}
                        key={i}
                      />
                    ))
                  ) : (
                    <>
                      <div className="w-full h-80 bg-white-100 border border-white-300 rounded-lg flex flex-col justify-center items-center p-4">
                        <div className="mb-5">
                          <Image src={ZeroStateLog} alt="" />
                        </div>
                        <p className="text-base text-dark-300 leading-6 font-medium">
                          No version have been added yet
                        </p>
                      </div>
                    </>
                  )}
                </>
              )
            ) : activeTab === 2 ? (
              logsLoading ? (
                <Loader className="h-[400px]" />
              ) : (
                <>
                  {logs?.records?.length && logs?.records?.length > 0 ? (
                    logs?.records.map((e, i) => <LogsCard logs={e} key={i} />)
                  ) : (
                    <>
                      <div className="w-full h-80 bg-white-100 border border-white-300 rounded-lg flex flex-col justify-center items-center p-4">
                        <div className="mb-5">
                          <Image src={ZeroStateLog} alt="" />
                        </div>
                        <p className="text-base text-dark-300 leading-6 font-medium">
                          No logs have been added yet
                        </p>
                      </div>
                    </>
                  )}
                </>
              )
            ) : (
              ''
            )}
          </div>
        ) : (
          <Loader className="h-[400px]" />
        )}
      </div>
    </SideBarWrapper>
  );
};

export default DocumentView;
