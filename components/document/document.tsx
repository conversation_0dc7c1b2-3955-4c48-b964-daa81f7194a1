import moment from 'moment';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';

import ResetFilterIcon from '@/assets/outline/resetFilter';
import SettingIcon from '@/assets/outline/settting';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { IDocumentDetails, IDocumentSummary } from '@/interfaces/document';
import { fetchData } from '@/utils/api';
import { hasAccess } from '@/utils/roleAccessConfig';
import { getValueOrDefault } from '@/utils/table';
import { formatDate } from '@/utils/time';

import Breadcrumb from '../common/breadcrumb';
import PrimaryButton from '../common/button/primaryButton';
import SecondaryButton from '../common/button/secondaryButton';
import TertiaryButton from '../common/button/tertiaryButton';
import { Dialog, DialogTrigger } from '../common/dialog';
import SideBarWrapper from '../common/sidebar/layout';
import CommonTable from '../common/table';
import Tabs from '../common/tabs';
import { Tooltip, TooltipContent, TooltipTrigger } from '../common/tooltip';
import { DocumentFilterConstants } from './components/enum';
import CreateDocumentModal from './components/modals/createDocumentModal';

interface IDocumentData {
  summary: IDocumentSummary;
  records: IDocumentDetails[];
}

interface DocumentComponentProps {
  accessToken: string;
  filter: any;
}

const DocumentHub = () => {
  const [activeTab, setActiveTab] = useState<number>(0);
  const [createDocument, setCreateDocument] = useState(false);
  const { accessToken, user } = useAuthStore();
  const [filterKey, setFilterKey] = useState<string | undefined>(undefined);

  const [data, setData] = useState<IDocumentData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<any>(null);

  const getDocuments = async () => {
    setIsLoading(true);
    try {
      const filterValue = filterKey
        ? DocumentFilterConstants[
            filterKey as keyof typeof DocumentFilterConstants
          ]
        : {};

      const response = await fetchData(
        accessToken as string,
        'documents',
        filterValue,
      );
      setData(response?.data);
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (accessToken && filterKey) getDocuments();
  }, [filterKey, accessToken]);

  const router = useRouter();
  const columnDefs: Array<{
    headerName: string;
    field: string;
    sortable: boolean;
    resizable: boolean;
    getQuickFilterText?: (params: { data: IDocumentDetails }) => string;
    valueFormatter?: (params: { data: IDocumentDetails }) => string;
    filter?: boolean | string;
    filterValueGetter?: (params: { data: IDocumentDetails }) => string;
    cellRenderer?: (params: { data: IDocumentDetails }) => JSX.Element | string;
    minWidth?: number;
    flex?: number;
  }> = [
    {
      headerName: 'ID',
      field: 'doc_id',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params) => {
        return params.data.doc_id;
      },
      valueFormatter: (params) => getValueOrDefault(params.data, 'doc_id'),
      filter: false,
    },
    {
      headerName: 'Title',
      field: 'title',
      resizable: true,
      sortable: true,
      valueFormatter: (params) => getValueOrDefault(params.data, 'title'),
      filter: false,
      minWidth: 350,
      flex: 2,
    },
    {
      headerName: 'Department',
      field: 'department.name',
      resizable: true,
      sortable: true,
      filter: 'agMultiColumnFilter',
      valueFormatter: (params) =>
        getValueOrDefault(params.data.department, 'name'),
    },
    {
      headerName: 'Category',
      field: 'category.name',
      filter: 'agMultiColumnFilter',
      sortable: true,
      resizable: true,
      valueFormatter: (params) =>
        getValueOrDefault(params.data.category, 'name'),
    },
    {
      headerName: 'Processes',
      field: 'process.name',
      filter: 'agMultiColumnFilter',
      sortable: false,
      resizable: true,
      filterValueGetter: (params) => {
        return params?.data?.processes
          ? params?.data?.processes
              ?.map((process: { name: string }) => {
                return `${process.name}`;
              })
              .join(', ')
          : '-';
      },
      getQuickFilterText: (params) => {
        return params?.data?.processes
          ?.map((process) => {
            return `${process.name}`;
          })
          .join(', ');
      },
      valueFormatter: (params) => {
        return params?.data?.processes
          ? params?.data?.processes
              ?.map((process) => {
                return `${process.name}`;
              })
              .join(', ')
          : '-';
      },
    },
    {
      headerName: 'Review date',
      field: 'next_review_date',
      sortable: true,
      resizable: true,
      valueFormatter: (params) =>
        formatDate(getValueOrDefault(params.data, 'next_review_date'), false),

      cellRenderer: (params) => {
        const review_date = formatDate(
          getValueOrDefault(params.data, 'next_review_date'),
          false,
        );

        const days = moment(review_date).diff(new Date(), 'days');

        return Number.isNaN(days) ? (
          '-'
        ) : days <= 0 ? (
          <div>
            <span className="bg-red-500 bg-opacity-[15%] px-4 py-1 rounded-md ">
              {review_date}
            </span>
          </div>
        ) : days < 30 ? (
          <div>
            <span className="bg-yellow-500 bg-opacity-[15%] px-4 py-1 rounded-md ">
              {review_date}
            </span>
          </div>
        ) : (
          <div>{review_date}</div>
        );
      },
      filter: false,
    },
    {
      headerName: 'Status',
      field: 'status',
      sortable: true,
      resizable: true,
      filter: 'agMultiColumnFilter',
    },
    {
      headerName: 'Assignee',
      field: 'assignees.name',
      sortable: false,
      resizable: true,
      filter: 'agMultiColumnFilter',
      filterValueGetter: (params) => {
        return params?.data?.assignees
          ? params?.data?.assignees
              ?.map((process: { name: string }) => {
                return `${process.name}`;
              })
              .join(', ')
          : '-';
      },
      getQuickFilterText: (params) => {
        return params?.data?.assignees
          ?.map((process) => {
            return `${process.name}`;
          })
          .join(', ');
      },
      valueFormatter: (params) => {
        return params?.data?.assignees
          ? params?.data?.assignees
              ?.map((process) => {
                return `${process.name}`;
              })
              .join(', ')
          : '-';
      },
    },
    {
      headerName: 'Approver',
      field: 'approvers.name',
      resizable: true,
      sortable: false,
      filter: 'agMultiColumnFilter',
      filterValueGetter: (params) => {
        return params?.data?.approvers
          ? params?.data?.approvers
              ?.map((process: { name: string }) => {
                return `${process.name}`;
              })
              .join(', ')
          : '-';
      },
      getQuickFilterText: (params) => {
        return params?.data?.approvers
          ?.map((process) => {
            return `${process.name}`;
          })
          .join(', ');
      },
      valueFormatter: (params) => {
        return params?.data?.approvers
          ? params?.data?.approvers
              ?.map((process) => {
                return `${process.name}`;
              })
              .join(', ')
          : '-';
      },
    },
    {
      headerName: 'Date of publish',
      field: 'publish_date',
      resizable: true,
      sortable: true,
      valueFormatter: (params) =>
        formatDate(getValueOrDefault(params.data, 'publish_date'), false),
      filter: false,
    },
    {
      headerName: 'Version',
      field: 'document_version.version_number',
      resizable: true,
      sortable: true,
      valueFormatter: (params) =>
        getValueOrDefault(params.data.document_version, 'version_number'),
      filter: 'agMultiColumnFilter',
    },
  ];

  const breadcrumbData = [
    {
      name: 'Document Hub',
      link: '#',
    },
  ];

  const tabsData = [
    {
      name: `All docs (${data?.summary.total || 0})`,
      textColor: 'text-dark-100',
      filter: 'ALL',
      onClick: () => setFilterKey('ALL'),
    },
    {
      name: `My docs (${data?.summary.assigned_to_me_count || 0})`,
      textColor: 'text-dark-100',
      filter: 'ASSIGNED_TO_ME',
      onClick: () => setFilterKey('ASSIGNED_TO_ME'),
    },
    {
      name: `Pending approval (${
        data?.summary.waiting_for_approval_count || 0
      })`,
      textColor: 'text-dark-100',
      filter: 'NEEDS_APPROVAL',
      onClick: () => setFilterKey('NEEDS_APPROVAL'),
    },
    {
      name: `Approved (${data?.summary.approved_approval_count || 0})`,
      textColor: 'text-dark-100',
      filter: 'IS_APPROVED_APPROVAL',
      onClick: () => setFilterKey('IS_APPROVED_APPROVAL'),
    },
    {
      name: `Rejected (${data?.summary.rejected_approval_count || 0})`,
      textColor: 'text-dark-100',
      filter: 'IS_REJECTED_APPROVAL',
      onClick: () => setFilterKey('IS_REJECTED_APPROVAL'),
    },
    {
      name: `Review in 30 days (${data?.summary.review_date_30d_count || 0})`,
      textColor: 'text-[#F19413]',
      filter: 'REVIEW_IN_30_DAYS',
      onClick: () => setFilterKey('REVIEW_IN_30_DAYS'),
    },
    {
      name: `Past due (${data?.summary.review_date_overdue_count || 0})`,
      textColor: 'text-[#E05252]',
      filter: 'PAST_DUE',
      onClick: () => setFilterKey('PAST_DUE'),
    },
  ];

  const handleClick = (id: string) => {
    router.push({
      pathname: `/document/${id}`,
      query: router.query,
    });
  };

  useEffect(() => {
    if (router.query.filter) {
      const queryKey = router.query.filter;
      const matchedFilterKey = Object.keys(DocumentFilterConstants).find(
        (key) => key === queryKey,
      );

      if (matchedFilterKey) {
        setFilterKey(matchedFilterKey);
      }
      const matchedTabIndex = tabsData.findIndex(
        (tab) => tab.filter === queryKey,
      );
      if (matchedTabIndex !== -1) {
        setActiveTab(matchedTabIndex);
      }
    }
  }, [router.query.filter]);

  useEffect(() => {
    if (router.isReady && filterKey !== undefined) {
      const currentQuery = router.query;
      router.push({
        pathname: '/document',
        query: {
          ...currentQuery,
          filter: filterKey,
        },
      });
    }
  }, [filterKey, router.isReady]);

  useEffect(() => {
    if (filterKey === undefined && router.isReady && !router.query.filter) {
      setFilterKey('ALL');
    }
  }, [router.isReady, router.query.filter, filterKey]);

  if (error) {
    return (
      <div className="rounded-md border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
        <p className="p-4">{error.response?.data?.error}</p>
      </div>
    );
  }

  return (
    <SideBarWrapper>
      <div className="flex items-start justify-between my-5">
        <div className="flex flex-col">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            Document Hub
          </div>
        </div>
      </div>
      <div className="mt-5 mb-6">
        <Tabs
          tabsData={tabsData}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
        />
      </div>

      <div className=" mb-5">
        <CommonTable
          data={data}
          isLoading={isLoading}
          // @ts-expect-error dssd
          columnDefs={columnDefs}
          handleRowClick={(e: any) => handleClick(e.data?.id)}
          searchRightSideElement={
            <div className="flex gap-4">
              <Tooltip>
                <TooltipTrigger>
                  <TertiaryButton
                    icon={<ResetFilterIcon />}
                    text=""
                    size="medium"
                    buttonClasses="!p-2.5"
                    onClick={() => {
                      const query = router.query;
                      query.page = '0';
                      query.pageSize = '10';
                      router.push({
                        pathname: router.pathname,
                        query: query,
                      });
                    }}
                  />
                </TooltipTrigger>
                <TooltipContent>Reset table filter</TooltipContent>
              </Tooltip>
              {hasAccess(AccessActions.IsDocumentAdmin, user) && (
                <Tooltip>
                  <TooltipTrigger>
                    <Link href={'/document-administration'}>
                      <SecondaryButton
                        icon={<SettingIcon />}
                        text=""
                        size="medium"
                        buttonClasses="!p-2.5"
                      />
                    </Link>
                  </TooltipTrigger>
                  <TooltipContent>Document Administration</TooltipContent>
                </Tooltip>
              )}

              {hasAccess(AccessActions.CanEditDocument, user) && (
                <Dialog open={createDocument} onOpenChange={setCreateDocument}>
                  <DialogTrigger asChild>
                    <PrimaryButton
                      text="New Document"
                      buttonClasses="!px-5 !py-2"
                    />
                  </DialogTrigger>
                  <CreateDocumentModal
                    reFetch={getDocuments}
                    open={createDocument}
                    setOpenEdit={setCreateDocument}
                  />
                </Dialog>
              )}
            </div>
          }
        />
      </div>
    </SideBarWrapper>
  );
};

export default DocumentHub;
