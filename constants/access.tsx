export enum AccessActions {
  CanAddOrEditProducts = 'CanAddOrEditProducts',
  CanEditSpecificProduct = 'CanEditSpecificProduct',
  CanDeleteSpecificProduct = 'CanDeleteSpecificProduct',
  CanAddOrEditAssets = 'CanAddOrEditAssets',
  IsAssetAdmin = 'IsAssetAdmin',
  CanAddOrEditInventory = 'CanAddOrEditInventory',
  IsInventoryAdmin = 'IsInventoryAdmin',
  CanAddOrEditPeople = 'CanAddOrEditPeople',
  IsPeopleAdmin = 'IsPeopleAdmin',
  IsDocumentAdmin = 'IsDocumentAdmin',
  IsDocumentEditor = 'IsDocumentEditor',
  IsDocumentViewer = 'IsDocumentViewer',
  CanEditDocument = 'CanEditDocument',
  CanUploadDocument = 'CanUploadDocument',
  CanLinkDocument = 'CanLinkDocument',
  CanAddOrEditWorkOrders = 'CanAddOrEditWorkOrders',
  CanEditSpecificWorkOrder = 'CanEditSpecificWorkOrder',
  CanDeleteSpecificWorkOrder = 'CanDeleteSpecificWorkOrder',
  IsAuditor = 'IsAuditor',
  IsAuditee = 'IsAuditee',
  CanEditDeleteCloseStartAudit = 'CanEditDeleteCloseStartAudit',
  CanCreateAudit = 'CanCreateAudit',
  CanCreateUser = 'CanCreateUser',
  // for sidebar
  ShowSidebarDocument = 'ShowSidebarDocument',
  ShowSidebarStandard = 'ShowSidebarStandard',
  ShowSidebarPeople = 'ShowSidebarPeople',
  ShowSidebarAsset = 'ShowSidebarAsset',
  ShowSidebarInventory = 'ShowSidebarInventory',
  ShowSidebarMaster = 'ShowSidebarMaster',
  ShowSidebarProduction = 'ShowSidebarProduction',
  ShowSidebarImprovement = 'ShowSidebarImprovement',
  ShowSidebarAudit = 'ShowSidebarAudit',
  ShowSidebarVendor = 'ShowSidebarVendor',
  VendorAdmin = 'VendorAdmin',
  VendorEditor = 'VendorEditor',
}
