import { GET_CURRENT_USER } from "@/utils/api";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { useAuthStore } from "./authStore";

interface GlobalProviderProps {
  children: React.ReactNode;
}

const unAuthPages = ["/privacy", "/terms"];

const GlobalProvider: React.FC<GlobalProviderProps> = ({ children }) => {
  const setAuth = useAuthStore((state) => state.setAuth);
  const clearAuth = useAuthStore((state) => state.clearAuth);
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const { isLoading, setIsLoading, user, accessToken } = useAuthStore();

  useEffect(() => {
    const fetchSessionAndToken = async () => {
      setIsLoading(true);
      try {
        const refreshResponse = await fetch("/api/auth/refresh");

        if (refreshResponse.status === 401 || refreshResponse.status === 500) {
          if (!unAuthPages.includes(router.pathname)) {
            router.push("/login");
          }
          setIsLoading(false);
          return;
        }

        const { accessToken } = await refreshResponse.json();

        const mockUser =
          typeof window !== "undefined"
            ? localStorage.getItem("x-mock-user")
            : null;

        const headers: Record<string, string> = {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
          ...(mockUser ? { "x-mock-user": mockUser } : {}),
        };
        if (accessToken) {
          const userResponse = await fetch(GET_CURRENT_USER, { headers });
          const user = await userResponse.json();
          setAuth(user.record, accessToken);
          setIsLoading(false);
        }
      } catch (err: any) {
        clearAuth();
        console.error("Error fetching session or token:", err);
        setError(err.message);
        setIsLoading(false);
      }
    };

    fetchSessionAndToken();
  }, []);

  return <>{children}</>;
};

export default GlobalProvider;
