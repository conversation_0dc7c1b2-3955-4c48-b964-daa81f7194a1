import { TCurrentUser } from '@/interfaces/user';
import { create } from 'zustand';

export interface AuthState {
  orgInfo: string | null;
  user: TCurrentUser | null;
  accessToken: string | null;
  isLoading: boolean;
  setOrg: (orgInfo: string) => void;
  setAuth: (user: TCurrentUser, accessToken: string) => void;
  clearAuth: () => void;
  setIsLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>((set) => ({
  orgInfo: null,
  user: null,
  accessToken: null,
  isLoading: true,
  setOrg: (orgInfo: string) => set({ orgInfo }),
  setAuth: (user, accessToken) => set({ user, accessToken }),
  clearAuth: () => set({ user: null, accessToken: null }),
  setIsLoading: (loading: boolean) => set({ isLoading: loading }),
}));
