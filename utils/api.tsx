import axios from 'axios';

export const GET_PRESIGNED_URL = `${process.env.NEXT_PUBLIC_URL}/${process.env.NEXT_PUBLIC_VERSION}/file/presigned-url`;
export const GET_ORGANIZATION = `${process.env.NEXT_PUBLIC_URL}/${process.env.NEXT_PUBLIC_VERSION}/orgs`;
export const GET_CURRENT_USER = `${process.env.NEXT_PUBLIC_URL}/${
  process.env.NEXT_PUBLIC_VERSION
}/${'users/me'}`;

export const fetchData = async (
  accessToken: string,
  endpoint: string,
  query?: Record<string, string[] | string | undefined>,
) => {
  const baseUrl = process.env.NEXT_PUBLIC_URL;
  const productVersion = process.env.NEXT_PUBLIC_VERSION;
  try {
    const response = await axios.get(
      `${baseUrl}/${productVersion}/${endpoint}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`, // Include the token
          'Content-Type': 'application/json',
        },
        params: query,
      },
    );

    return response;
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};
