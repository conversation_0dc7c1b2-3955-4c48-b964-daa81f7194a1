export interface IDocumentSummary {
  total: number;
  not_uploaded_count: number;
  draft_count: number;
  published_count: number;
  review_date_30d_count: number;
  review_date_60d_count: number;
  review_date_overdue_count: number;
  waiting_for_approval_count: number;
  assigned_to_me_count: number;
  rejected_approval_count: number;
  approved_approval_count: number;
}

export interface IDocumentDetails {
  id: string;
  created_on: string; // ISO date string
  last_modified_on: string; // ISO date string
  title: string;
  review_period: number;
  origin: 'Internal' | 'External';
  status: 'Not Uploaded' | 'Draft' | 'Published' | 'Approved' | 'Rejected';
  doc_id: string;
  last_review_date: string;
  department: {
    id: string;
    name: string;
  };
  category: {
    id: string;
    name: string;
  };
  processes: {
    id: string;
    name: string;
  }[];
  assignees: {
    id: string;
    full_name: string;
    name: string;
  }[];
  approvers: {
    id: string;
    full_name: string;
    name: string;
  }[];
  publish_date: string; // ISO date string
  next_review_date: string; // ISO date string
  document_version: {
    id: string;
    created_by: string; // User ID
    last_modified_by: string; // User ID
    created_on: string; // ISO date string
    last_modified_on: string; // ISO date string
    version_number: number;
    document: string; // Document ID
    file_path: string; // Path to the file
    file_extension: string; // File extension (e.g., docx)
  };
  created_by: {
    id: string;
    full_name: string;
    name: string;
  };
  approval: {
    flow: string;
    record_type: 'Document';
    approver: string;
    file_extension: string;
    company: string;
    id: string;
    created_on: string;
    last_modified_on: string;
    status: 'Pending' | 'Approved' | 'Rejected'; // Assuming possible status values
    record_id: string;
    file_path: string;
    version: number;
    created_by: string;
    last_modified_by: string;
    payload: {
      file_extension: string;
      file_path: string;
      version: number;
    };
    approvers: [];
  };
}

export interface ILogs {
  company: string;
  action: string;
  created_by: {
    id: string;
    full_name: string;
    name: string;
  };
  created_on: string;
  description: string;
  comment: string;
  document: string;
  document_version: string;
  id: string;
  last_modified_on: string;
}

export interface IDocumentVersion {
  file_path: string;
  id: string;
  title: string;
  version_number: number;
  file_extension: string;
}
