import { IAttachment } from "./misc";

export interface TWorkOrder {
    id: string;
    order_no: string;
    reference_no: string;
    customer_name: string;
    status:string;
    uom: string;
    quantity: number;
    priority: "Low" | "Medium" | "High"; // Enum-like typing for priority
    start_date: string; // ISO 8601 date string
    delivery_date: string; // ISO 8601 date string
    name: string; // UUID as a string
    managers: {
      full_name: string;
      id: string;
      name: string;
    }[]; // Array of UUIDs
    attachments:IAttachment[]
  }

  export interface TStep {
    id:string;
    name:string;
    status?:string;
    action?: string;
    instruction?: string;
    remark?: string;
    is_enabled?:boolean;
    assignees?:string[];
    approvers?:string[];
  }

  export interface IWorkInstruction {
    sequence_no: number;
    description: string;
    compliant: boolean;
    remark: string;
    attachments?: IAttachment[];
    completion_date: string;
    done_by: string;
    id:string;
  }