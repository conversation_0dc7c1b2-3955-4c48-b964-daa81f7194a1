import { Process } from './standard';

export interface Audit {
  record: IAuditDetails;
}

export interface IAuditDetails {
  id: string;
  created_by: string;
  last_modified_by: string;
  created_on: string;
  last_modified_on: string;
  name: string;
  start_date: string;
  notification_date: string;
  status: string;
  percent_complete: number;
  audit_type: string;
  remark: string;
  auditors: {
    id: string;
    full_name?: string;
    name: string;
  };
  auditees: {
    id: string;
    full_name: string;
    name: string;
  };
  standards: AuditStandard[];
}

// ------

type Clauses = {
  id: string;
  clause_no: string;
  title: string;
  is_compliant: boolean;
  sub_clauses: SubClause[];
};

export interface AuditStandard {
  id: string;
  title: string;
  clause_no: string;
  clauses: Clauses[];
  sub_clauses?: SubClause[];
  documents?: Document[];
  is_compliant: boolean;
}

export interface Document {
  id: string;
  last_modified_by: User;
  created_on: string;
  last_modified_on: string;
  doc_id: string;
  title: string;
  assignee: User;
  approver: User;
  review_period: number;
  origin: string;
  status: string;
  is_compliant: boolean;
}

export interface DocumentData {
  [key: string]: Document;
}

export interface Comment {
  id: string;
  created_by: User;
  last_modified_by: User;
  created_on: string;
  last_modified_on: string;
  description: string;
  attachements: any[]; // Use a more specific type if attachment structure is known
}

export interface Requirements {
  records: AuditRequirement[];
}

export interface AuditRequirement {
  id: string;
  created_on: string;
  description: string;
  status: string;
  standard: AuditStandard;
  sub_clause: SubClause;
  comments: Comment[];
  justification: string;
  action: string;
  remark: string;
  category: string;
  is_compliant: boolean;
}

export interface Data {
  [key: string]: AuditRequirement;
}

export interface User {
  id: string;
  role: string;
  full_name: string;
  name: string;
}

export interface SubClause {
  id: string;
  clause_no: string;
  title: string;
  status: string;
  description: string;
  question: string;
  documents?: Document[];
  processes: Process[];
  is_compliant: boolean;
}

export interface Findings {
  records: NonConformity[];
}

export interface NonConformity {
  id: string;
  created_by: string;
  last_modified_by: string;
  created_on: string;
  last_modified_on: string;
  nc_id: string;
  standard: Standard;
  sub_clause: SubClause;
  status: string;
  category: string;
  description: string;
  comments: Comment[];
  justification: string;
  action: string;
  remark: string;
  capa: string;
}

export type NonConformities = Record<string, NonConformity>;

export interface Standard {
  id: string;
  title: string;
}

export interface AuditRecord {
  id: string;
  created_by: string;
  last_modified_by: string;
  created_on: string;
  last_modified_on: string;
  name: string;
  start_date: string;
  notification_date: string;
  status: string;
  percent_complete: number;
  audit_type: string;
  auditors: User[];
  auditees: User[];
  standards: Standard[];
  remark: string;
}

export interface AuditData {
  record: AuditRecord;
}

export interface AuditData {
  [key: string]: AuditRecord;
}
